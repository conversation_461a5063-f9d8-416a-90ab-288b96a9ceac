/**
 * ملف JavaScript الرئيسي لنظام HealthKey
 * يحتوي على الوظائف العامة والتفاعلات الأساسية
 */

// تشغيل الكود عند تحميل الصفحة
$(document).ready(function() {
    // تهيئة التطبيق
    HealthKey.init();
});

// كائن التطبيق الرئيسي
const HealthKey = {
    // تهيئة التطبيق
    init: function() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupFormValidation();
        this.setupAjaxDefaults();
    },

    // إعداد مستمعي الأحداث
    setupEventListeners: function() {
        // إغلاق التنبيهات تلقائياً
        this.autoCloseAlerts();
        
        // تأكيد الحذف
        this.setupDeleteConfirmation();
        
        // تحسين النماذج
        this.setupFormEnhancements();
        
        // إعداد البحث المباشر
        this.setupLiveSearch();
    },

    // تهيئة المكونات
    initializeComponents: function() {
        // تهيئة tooltips
        if (typeof bootstrap !== 'undefined') {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // تهيئة popovers
        if (typeof bootstrap !== 'undefined') {
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        }
    },

    // إعداد التحقق من النماذج
    setupFormValidation: function() {
        // التحقق من النماذج عند الإرسال
        $('form[data-validate="true"]').on('submit', function(e) {
            if (!HealthKey.validateForm(this)) {
                e.preventDefault();
                return false;
            }
        });

        // التحقق المباشر من الحقول
        $('input[data-validate], textarea[data-validate], select[data-validate]').on('blur', function() {
            HealthKey.validateField(this);
        });
    },

    // إعداد AJAX الافتراضي
    setupAjaxDefaults: function() {
        // إعدادات AJAX العامة
        $.ajaxSetup({
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            error: function(xhr, status, error) {
                HealthKey.showAlert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
            }
        });
    },

    // إغلاق التنبيهات تلقائياً
    autoCloseAlerts: function() {
        $('.alert[data-auto-close]').each(function() {
            const alert = $(this);
            const delay = alert.data('auto-close') || 5000;
            
            setTimeout(function() {
                alert.fadeOut();
            }, delay);
        });
    },

    // إعداد تأكيد الحذف
    setupDeleteConfirmation: function() {
        $(document).on('click', '[data-confirm-delete]', function(e) {
            e.preventDefault();
            
            const element = $(this);
            const message = element.data('confirm-delete') || 'هل أنت متأكد من الحذف؟';
            
            if (confirm(message)) {
                // إذا كان رابط، انتقل إليه
                if (element.is('a')) {
                    window.location.href = element.attr('href');
                }
                // إذا كان زر في نموذج، أرسل النموذج
                else if (element.is('button') && element.closest('form').length) {
                    element.closest('form').submit();
                }
            }
        });
    },

    // تحسينات النماذج
    setupFormEnhancements: function() {
        // إضافة مؤشر التحميل عند إرسال النماذج
        $('form').on('submit', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.text();
            
            submitBtn.prop('disabled', true)
                    .html('<span class="spinner-border spinner-border-sm me-2"></span>جاري المعالجة...');
            
            // إعادة تفعيل الزر بعد 10 ثوان (في حالة عدم إعادة التوجيه)
            setTimeout(function() {
                submitBtn.prop('disabled', false).text(originalText);
            }, 10000);
        });

        // تحسين حقول كلمة المرور
        $('.password-toggle').on('click', function() {
            const input = $(this).siblings('input');
            const icon = $(this).find('i');
            
            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                icon.removeClass('bi-eye').addClass('bi-eye-slash');
            } else {
                input.attr('type', 'password');
                icon.removeClass('bi-eye-slash').addClass('bi-eye');
            }
        });
    },

    // إعداد البحث المباشر
    setupLiveSearch: function() {
        $('[data-live-search]').on('input', function() {
            const input = $(this);
            const target = input.data('live-search');
            const query = input.val().toLowerCase();
            
            $(target).each(function() {
                const text = $(this).text().toLowerCase();
                if (text.includes(query)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });
    },

    // التحقق من النموذج
    validateForm: function(form) {
        let isValid = true;
        const $form = $(form);
        
        // إزالة رسائل الخطأ السابقة
        $form.find('.is-invalid').removeClass('is-invalid');
        $form.find('.invalid-feedback').remove();
        
        // التحقق من الحقول المطلوبة
        $form.find('[required]').each(function() {
            if (!this.value.trim()) {
                HealthKey.showFieldError(this, 'هذا الحقل مطلوب');
                isValid = false;
            }
        });
        
        // التحقق من البريد الإلكتروني
        $form.find('input[type="email"]').each(function() {
            if (this.value && !HealthKey.isValidEmail(this.value)) {
                HealthKey.showFieldError(this, 'البريد الإلكتروني غير صحيح');
                isValid = false;
            }
        });
        
        // التحقق من تطابق كلمات المرور
        const password = $form.find('input[name="password"]').val();
        const confirmPassword = $form.find('input[name="confirm_password"]').val();
        
        if (password && confirmPassword && password !== confirmPassword) {
            HealthKey.showFieldError($form.find('input[name="confirm_password"]')[0], 'كلمات المرور غير متطابقة');
            isValid = false;
        }
        
        return isValid;
    },

    // التحقق من حقل واحد
    validateField: function(field) {
        const $field = $(field);
        const value = field.value.trim();
        
        // إزالة رسائل الخطأ السابقة
        $field.removeClass('is-invalid');
        $field.siblings('.invalid-feedback').remove();
        
        // التحقق من الحقول المطلوبة
        if (field.hasAttribute('required') && !value) {
            HealthKey.showFieldError(field, 'هذا الحقل مطلوب');
            return false;
        }
        
        // التحقق من البريد الإلكتروني
        if (field.type === 'email' && value && !HealthKey.isValidEmail(value)) {
            HealthKey.showFieldError(field, 'البريد الإلكتروني غير صحيح');
            return false;
        }
        
        return true;
    },

    // عرض خطأ في الحقل
    showFieldError: function(field, message) {
        const $field = $(field);
        $field.addClass('is-invalid');
        $field.after('<div class="invalid-feedback">' + message + '</div>');
    },

    // التحقق من صحة البريد الإلكتروني
    isValidEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // عرض تنبيه
    showAlert: function(message, type = 'info', autoClose = true) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة التنبيه في أعلى المحتوى
        $('.main-content').prepend(alertHtml);
        
        // إغلاق تلقائي
        if (autoClose) {
            setTimeout(function() {
                $('.alert').first().fadeOut();
            }, 5000);
        }
    },

    // تحميل محتوى عبر AJAX
    loadContent: function(url, container, showLoading = true) {
        const $container = $(container);
        
        if (showLoading) {
            $container.html('<div class="text-center p-4"><div class="spinner-border" role="status"></div></div>');
        }
        
        $.get(url)
            .done(function(data) {
                $container.html(data);
            })
            .fail(function() {
                $container.html('<div class="alert alert-danger">فشل في تحميل المحتوى</div>');
            });
    },

    // تنسيق التاريخ
    formatDate: function(date, format = 'YYYY-MM-DD') {
        if (!date) return '';
        
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        
        return format.replace('YYYY', year)
                    .replace('MM', month)
                    .replace('DD', day);
    },

    // تنسيق الوقت
    formatTime: function(time) {
        if (!time) return '';
        
        const [hours, minutes] = time.split(':');
        const hour12 = hours % 12 || 12;
        const ampm = hours >= 12 ? 'م' : 'ص';
        
        return `${hour12}:${minutes} ${ampm}`;
    },

    // تنسيق الأرقام
    formatNumber: function(number, decimals = 0) {
        return Number(number).toLocaleString('ar-SA', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }
};

// وظائف مساعدة عامة
window.showAlert = HealthKey.showAlert;
window.loadContent = HealthKey.loadContent;
