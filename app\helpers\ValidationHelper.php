<?php

/**
 * مساعد التحقق من صحة البيانات
 * يوفر وظائف شاملة للتحقق من صحة المدخلات وتنظيفها
 */
class ValidationHelper
{
    private static $errors = [];
    private static $customMessages = [];

    /**
     * تعيين رسائل خطأ مخصصة
     */
    public static function setCustomMessages($messages)
    {
        self::$customMessages = array_merge(self::$customMessages, $messages);
    }

    /**
     * التحقق من صحة البيانات
     */
    public static function validate($data, $rules)
    {
        self::$errors = [];
        
        foreach ($rules as $field => $fieldRules) {
            $value = $data[$field] ?? null;
            self::validateField($field, $value, $fieldRules, $data);
        }
        
        return self::$errors;
    }

    /**
     * التحقق من صحة حقل واحد
     */
    private static function validateField($field, $value, $rules, $allData = [])
    {
        foreach ($rules as $rule => $ruleValue) {
            if ($rule === 'message') continue; // تخطي رسائل الخطأ المخصصة
            
            $isValid = true;
            $errorMessage = '';
            
            switch ($rule) {
                case 'required':
                    if ($ruleValue && self::isEmpty($value)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'هذا الحقل مطلوب');
                    }
                    break;
                    
                case 'email':
                    if ($ruleValue && !empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'البريد الإلكتروني غير صحيح');
                    }
                    break;
                    
                case 'min_length':
                    if (!empty($value) && strlen($value) < $ruleValue) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, "يجب أن يكون الحقل $ruleValue أحرف على الأقل");
                    }
                    break;
                    
                case 'max_length':
                    if (!empty($value) && strlen($value) > $ruleValue) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, "يجب أن يكون الحقل أقل من $ruleValue حرف");
                    }
                    break;
                    
                case 'min':
                    if (!empty($value) && is_numeric($value) && $value < $ruleValue) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, "القيمة يجب أن تكون $ruleValue أو أكثر");
                    }
                    break;
                    
                case 'max':
                    if (!empty($value) && is_numeric($value) && $value > $ruleValue) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, "القيمة يجب أن تكون $ruleValue أو أقل");
                    }
                    break;
                    
                case 'numeric':
                    if ($ruleValue && !empty($value) && !is_numeric($value)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'يجب أن يكون الحقل رقماً');
                    }
                    break;
                    
                case 'integer':
                    if ($ruleValue && !empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'يجب أن يكون الحقل رقماً صحيحاً');
                    }
                    break;
                    
                case 'alpha':
                    if ($ruleValue && !empty($value) && !ctype_alpha(str_replace(' ', '', $value))) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'يجب أن يحتوي الحقل على أحرف فقط');
                    }
                    break;
                    
                case 'alpha_numeric':
                    if ($ruleValue && !empty($value) && !ctype_alnum(str_replace(' ', '', $value))) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'يجب أن يحتوي الحقل على أحرف وأرقام فقط');
                    }
                    break;
                    
                case 'date':
                    if ($ruleValue && !empty($value) && !self::isValidDate($value)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'تاريخ غير صحيح');
                    }
                    break;
                    
                case 'time':
                    if ($ruleValue && !empty($value) && !self::isValidTime($value)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'وقت غير صحيح');
                    }
                    break;
                    
                case 'url':
                    if ($ruleValue && !empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'رابط غير صحيح');
                    }
                    break;
                    
                case 'phone':
                    if ($ruleValue && !empty($value) && !self::isValidPhone($value)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'رقم هاتف غير صحيح');
                    }
                    break;
                    
                case 'match':
                    if (!empty($value) && $value !== ($allData[$ruleValue] ?? '')) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'الحقلان غير متطابقان');
                    }
                    break;
                    
                case 'in':
                    if (!empty($value) && !in_array($value, $ruleValue)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'قيمة غير صحيحة');
                    }
                    break;
                    
                case 'regex':
                    if (!empty($value) && !preg_match($ruleValue, $value)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'تنسيق غير صحيح');
                    }
                    break;
                    
                case 'unique':
                    if (!empty($value) && !self::isUnique($field, $value, $ruleValue)) {
                        $isValid = false;
                        $errorMessage = self::getMessage($field, $rule, 'هذه القيمة مستخدمة بالفعل');
                    }
                    break;
                    
                case 'file':
                    if ($ruleValue && isset($_FILES[$field])) {
                        $fileValidation = self::validateFile($_FILES[$field], $ruleValue);
                        if (!$fileValidation['valid']) {
                            $isValid = false;
                            $errorMessage = $fileValidation['message'];
                        }
                    }
                    break;
            }
            
            if (!$isValid) {
                self::$errors[$field] = $errorMessage;
                break; // توقف عند أول خطأ للحقل
            }
        }
    }

    /**
     * الحصول على رسالة خطأ مخصصة أو افتراضية
     */
    private static function getMessage($field, $rule, $default)
    {
        $key = "$field.$rule";
        return self::$customMessages[$key] ?? self::$customMessages[$rule] ?? $default;
    }

    /**
     * التحقق من كون القيمة فارغة
     */
    private static function isEmpty($value)
    {
        return $value === null || $value === '' || (is_array($value) && empty($value));
    }

    /**
     * التحقق من صحة التاريخ
     */
    private static function isValidDate($date)
    {
        $formats = ['Y-m-d', 'Y/m/d', 'd-m-Y', 'd/m/Y'];
        
        foreach ($formats as $format) {
            $dateTime = DateTime::createFromFormat($format, $date);
            if ($dateTime && $dateTime->format($format) === $date) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * التحقق من صحة الوقت
     */
    private static function isValidTime($time)
    {
        return preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/', $time);
    }

    /**
     * التحقق من صحة رقم الهاتف
     */
    private static function isValidPhone($phone)
    {
        // أرقام الهاتف السعودية
        $patterns = [
            '/^05[0-9]{8}$/',           // 05xxxxxxxx
            '/^\+9665[0-9]{8}$/',       // +9665xxxxxxxx
            '/^9665[0-9]{8}$/',         // 9665xxxxxxxx
            '/^5[0-9]{8}$/'             // 5xxxxxxxx
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $phone)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * التحقق من تفرد القيمة في قاعدة البيانات
     */
    private static function isUnique($field, $value, $config)
    {
        try {
            $db = Database::getInstance();
            $table = $config['table'];
            $column = $config['column'] ?? $field;
            $except = $config['except'] ?? null;
            
            $query = "SELECT COUNT(*) as count FROM $table WHERE $column = :value";
            $params = [':value' => $value];
            
            if ($except) {
                $query .= " AND id != :except";
                $params[':except'] = $except;
            }
            
            $result = $db->selectOne($query, $params);
            return (int)$result['count'] === 0;
            
        } catch (Exception $e) {
            return true; // في حالة الخطأ، نفترض أن القيمة فريدة
        }
    }

    /**
     * التحقق من صحة الملف
     */
    private static function validateFile($file, $rules)
    {
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['valid' => false, 'message' => 'خطأ في رفع الملف'];
        }
        
        // التحقق من الحجم
        if (isset($rules['max_size']) && $file['size'] > $rules['max_size']) {
            $maxSizeMB = round($rules['max_size'] / 1024 / 1024, 2);
            return ['valid' => false, 'message' => "حجم الملف يجب أن يكون أقل من {$maxSizeMB} ميجابايت"];
        }
        
        // التحقق من نوع الملف
        if (isset($rules['types'])) {
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($extension, $rules['types'])) {
                $allowedTypes = implode(', ', $rules['types']);
                return ['valid' => false, 'message' => "أنواع الملفات المسموحة: $allowedTypes"];
            }
        }
        
        return ['valid' => true, 'message' => ''];
    }

    /**
     * تنظيف البيانات
     */
    public static function sanitize($data)
    {
        if (is_array($data)) {
            return array_map([self::class, 'sanitize'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    /**
     * تنظيف النص من HTML
     */
    public static function stripTags($data, $allowedTags = '')
    {
        if (is_array($data)) {
            return array_map(function($item) use ($allowedTags) {
                return self::stripTags($item, $allowedTags);
            }, $data);
        }
        
        return strip_tags($data, $allowedTags);
    }

    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePasswordStrength($password)
    {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }
        
        if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
        }
        
        return $errors;
    }

    /**
     * التحقق من الرقم الوطني السعودي
     */
    public static function validateSaudiNationalId($id)
    {
        if (!preg_match('/^[12][0-9]{9}$/', $id)) {
            return false;
        }
        
        $sum = 0;
        for ($i = 0; $i < 9; $i++) {
            $digit = (int)$id[$i];
            if ($i % 2 === 0) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit = $digit % 10 + 1;
                }
            }
            $sum += $digit;
        }
        
        $checkDigit = (10 - ($sum % 10)) % 10;
        return $checkDigit == (int)$id[9];
    }

    /**
     * التحقق من IBAN السعودي
     */
    public static function validateSaudiIBAN($iban)
    {
        $iban = strtoupper(str_replace(' ', '', $iban));
        
        if (!preg_match('/^SA[0-9]{22}$/', $iban)) {
            return false;
        }
        
        // خوارزمية MOD-97
        $rearranged = substr($iban, 4) . substr($iban, 0, 4);
        $numeric = '';
        
        for ($i = 0; $i < strlen($rearranged); $i++) {
            $char = $rearranged[$i];
            if (ctype_alpha($char)) {
                $numeric .= ord($char) - ord('A') + 10;
            } else {
                $numeric .= $char;
            }
        }
        
        return bcmod($numeric, '97') === '1';
    }

    /**
     * الحصول على جميع الأخطاء
     */
    public static function getErrors()
    {
        return self::$errors;
    }

    /**
     * التحقق من وجود أخطاء
     */
    public static function hasErrors()
    {
        return !empty(self::$errors);
    }

    /**
     * الحصول على خطأ محدد
     */
    public static function getError($field)
    {
        return self::$errors[$field] ?? null;
    }

    /**
     * مسح الأخطاء
     */
    public static function clearErrors()
    {
        self::$errors = [];
    }

    /**
     * إضافة خطأ يدوياً
     */
    public static function addError($field, $message)
    {
        self::$errors[$field] = $message;
    }

    /**
     * قواعد التحقق الشائعة
     */
    public static function getCommonRules()
    {
        return [
            'email' => [
                'required' => true,
                'email' => true,
                'max_length' => 255
            ],
            'password' => [
                'required' => true,
                'min_length' => 8
            ],
            'name' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 50,
                'alpha' => true
            ],
            'phone' => [
                'required' => true,
                'phone' => true
            ],
            'national_id' => [
                'regex' => '/^[12][0-9]{9}$/'
            ]
        ];
    }

    /**
     * التحقق السريع من البريد الإلكتروني
     */
    public static function isValidEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * التحقق السريع من الرقم
     */
    public static function isValidNumber($number, $min = null, $max = null)
    {
        if (!is_numeric($number)) {
            return false;
        }
        
        if ($min !== null && $number < $min) {
            return false;
        }
        
        if ($max !== null && $number > $max) {
            return false;
        }
        
        return true;
    }

    /**
     * تنسيق رقم الهاتف
     */
    public static function formatPhone($phone)
    {
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        if (strlen($phone) === 9 && $phone[0] === '5') {
            return '05' . $phone;
        }
        
        if (strlen($phone) === 12 && substr($phone, 0, 4) === '9665') {
            return '0' . substr($phone, 3);
        }
        
        return $phone;
    }
}
