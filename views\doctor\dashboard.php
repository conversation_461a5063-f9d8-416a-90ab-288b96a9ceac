<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">مرحباً، د. <?= htmlspecialchars($doctor['first_name']) ?></h1>
        <p class="text-muted">
            <?= !empty($doctor['specialization']) ? htmlspecialchars($doctor['specialization']) : 'طبيب عام' ?>
        </p>
    </div>
    <div>
        <?php if ($unreadNotifications > 0): ?>
            <a href="<?= App::url('doctor/notifications') ?>" class="btn btn-outline-primary position-relative">
                <i class="bi bi-bell"></i>
                الإشعارات
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    <?= $unreadNotifications ?>
                </span>
            </a>
        <?php endif; ?>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($todayAppointments) ?></h4>
                        <p class="mb-0">مواعيد اليوم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-day display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['appointments']['total'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي المواعيد</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['prescriptions']['total'] ?? 0 ?></h4>
                        <p class="mb-0">الوصفات الطبية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['medical_records']['total'] ?? 0 ?></h4>
                        <p class="mb-0">السجلات الطبية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard2-pulse display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- مواعيد اليوم -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-day me-2"></i>
                    مواعيد اليوم
                </h5>
                <a href="<?= App::url('doctor/appointments') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($todayAppointments)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>المريض</th>
                                    <th>السبب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($todayAppointments as $appointment): ?>
                                    <tr>
                                        <td>
                                            <strong><?= DateHelper::formatTime($appointment['appointment_time']) ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?= htmlspecialchars($appointment['patient_name']) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= htmlspecialchars($appointment['patient_phone']) ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <?= htmlspecialchars($appointment['reason'] ?? 'غير محدد') ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= Appointment::getStatusColor($appointment['status']) ?>">
                                                <?= Appointment::getStatusLabel($appointment['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($appointment['status'] === 'scheduled'): ?>
                                                    <button class="btn btn-outline-success" onclick="confirmAppointment(<?= $appointment['id'] ?>)">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <?php if (in_array($appointment['status'], ['scheduled', 'confirmed'])): ?>
                                                    <button class="btn btn-outline-primary" onclick="completeAppointment(<?= $appointment['id'] ?>)">
                                                        <i class="bi bi-check-circle"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <a href="<?= App::url('doctor/patient/' . $appointment['patient_id']) ?>" class="btn btn-outline-info">
                                                    <i class="bi bi-person"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-calendar-x display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مواعيد اليوم</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- الإشعارات الحديثة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-bell me-2"></i>
                    الإشعارات الحديثة
                </h5>
                <a href="<?= App::url('doctor/notifications') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($notifications)): ?>
                    <?php foreach ($notifications as $notification): ?>
                        <div class="d-flex align-items-start mb-3 p-3 bg-light rounded <?= !$notification['is_read'] ? 'border-start border-primary border-3' : '' ?>">
                            <div class="me-3">
                                <i class="bi <?= Notification::getTypeIcon($notification['type']) ?> text-<?= Notification::getTypeColor($notification['type']) ?>"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 small"><?= htmlspecialchars($notification['title']) ?></h6>
                                <p class="mb-1 text-muted small"><?= htmlspecialchars($notification['message']) ?></p>
                                <p class="mb-0 text-muted small">
                                    <?= Notification::formatTime($notification['created_at']) ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-bell-slash display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد إشعارات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الوصفات الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-prescription2 me-2"></i>
                    الوصفات الحديثة
                </h5>
                <a href="<?= App::url('doctor/prescriptions') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentPrescriptions)): ?>
                    <?php foreach ($recentPrescriptions as $prescription): ?>
                        <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="<?= App::url('doctor/prescription/' . $prescription['id']) ?>" class="text-decoration-none">
                                        <?= htmlspecialchars($prescription['prescription_code']) ?>
                                    </a>
                                </h6>
                                <p class="mb-1 text-muted"><?= htmlspecialchars($prescription['patient_name']) ?></p>
                                <p class="mb-0 text-muted small">
                                    <i class="bi bi-calendar me-1"></i>
                                    <?= DateHelper::formatArabic($prescription['issue_date']) ?>
                                </p>
                            </div>
                            <div>
                                <span class="badge bg-<?= $prescription['status'] === 'active' ? 'success' : 'secondary' ?>">
                                    <?= $prescription['status'] === 'active' ? 'نشطة' : 'منتهية' ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-prescription display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد وصفات طبية</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- السجلات الطبية الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-clipboard2-pulse me-2"></i>
                    السجلات الطبية الحديثة
                </h5>
                <a href="<?= App::url('doctor/patients') ?>" class="btn btn-sm btn-outline-primary">
                    عرض المرضى
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentMedicalRecords)): ?>
                    <?php foreach ($recentMedicalRecords as $record): ?>
                        <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="<?= App::url('doctor/patient/' . $record['patient_id']) ?>" class="text-decoration-none">
                                        <?= htmlspecialchars($record['patient_name']) ?>
                                    </a>
                                </h6>
                                <p class="mb-1 text-muted small">
                                    <?= htmlspecialchars($record['diagnosis'] ?? 'غير محدد') ?>
                                </p>
                                <p class="mb-0 text-muted small">
                                    <i class="bi bi-calendar me-1"></i>
                                    <?= DateHelper::formatArabic($record['visit_date']) ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard2-x display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد سجلات طبية</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('doctor/appointments') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-calendar-check display-6 mb-2"></i>
                            <span>إدارة المواعيد</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('doctor/patients') ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-people display-6 mb-2"></i>
                            <span>المرضى</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('doctor/prescriptions') ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-prescription2 display-6 mb-2"></i>
                            <span>الوصفات الطبية</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('doctor/reports') ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-graph-up display-6 mb-2"></i>
                            <span>التقارير</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmAppointment(appointmentId) {
    if (confirm('هل تريد تأكيد هذا الموعد؟')) {
        fetch('<?= App::url('doctor/confirm-appointment') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'appointment_id=' + appointmentId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'error');
        });
    }
}

function completeAppointment(appointmentId) {
    const notes = prompt('ملاحظات الموعد (اختياري):');
    if (notes !== null) {
        fetch('<?= App::url('doctor/complete-appointment') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'appointment_id=' + appointmentId + '&notes=' + encodeURIComponent(notes)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'error');
        });
    }
}
</script>
