<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'لوحة تحكم المدير' ?> - HealthKey</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?= App::asset('css/admin.css') ?>" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= App::asset('images/favicon.ico') ?>">
</head>
<body class="admin-layout">
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand d-flex align-items-center" href="<?= App::url('admin/dashboard') ?>">
                <i class="bi bi-shield-check me-2"></i>
                <span class="fw-bold">HealthKey Admin</span>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Items -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?= App::isCurrentPath('admin/dashboard') ? 'active' : '' ?>" 
                           href="<?= App::url('admin/dashboard') ?>">
                            <i class="bi bi-speedometer2 me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= App::pathStartsWith('admin/users') ? 'active' : '' ?>" 
                           href="<?= App::url('admin/users') ?>">
                            <i class="bi bi-people me-1"></i>
                            المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= App::pathStartsWith('admin/appointments') ? 'active' : '' ?>" 
                           href="<?= App::url('admin/appointments') ?>">
                            <i class="bi bi-calendar-check me-1"></i>
                            المواعيد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= App::pathStartsWith('admin/prescriptions') ? 'active' : '' ?>" 
                           href="<?= App::url('admin/prescriptions') ?>">
                            <i class="bi bi-prescription2 me-1"></i>
                            الوصفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= App::pathStartsWith('admin/reports') ? 'active' : '' ?>" 
                           href="<?= App::url('admin/reports') ?>">
                            <i class="bi bi-graph-up me-1"></i>
                            التقارير
                        </a>
                    </li>
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <!-- Notifications -->
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="#" id="notificationsDropdown" 
                           role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-bell"></i>
                            <?php if (SessionHelper::get('unread_notifications', 0) > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?= SessionHelper::get('unread_notifications') ?>
                                </span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                            <li><h6 class="dropdown-header">الإشعارات</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?= App::url('admin/notifications') ?>">
                                    <i class="bi bi-bell me-2"></i>
                                    عرض جميع الإشعارات
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- User Profile -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" 
                           id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
                                <i class="bi bi-person text-primary"></i>
                            </div>
                            <span><?= SessionHelper::getUserName() ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="<?= App::url('admin/profile') ?>">
                                    <i class="bi bi-person-gear me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?= App::url('admin/settings') ?>">
                                    <i class="bi bi-gear me-2"></i>
                                    الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="<?= App::url('auth/logout') ?>">
                                    <i class="bi bi-box-arrow-right me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h5 class="text-white mb-0">
                <i class="bi bi-grid-3x3-gap me-2"></i>
                القائمة الرئيسية
            </h5>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/dashboard') ? 'active' : '' ?>" 
                       href="<?= App::url('admin/dashboard') ?>">
                        <i class="bi bi-speedometer2"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= App::pathStartsWith('admin/users') ? 'active' : '' ?>" 
                       href="<?= App::url('admin/users') ?>">
                        <i class="bi bi-people"></i>
                        <span>إدارة المستخدمين</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= App::pathStartsWith('admin/appointments') ? 'active' : '' ?>" 
                       href="<?= App::url('admin/appointments') ?>">
                        <i class="bi bi-calendar-check"></i>
                        <span>إدارة المواعيد</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= App::pathStartsWith('admin/prescriptions') ? 'active' : '' ?>" 
                       href="<?= App::url('admin/prescriptions') ?>">
                        <i class="bi bi-prescription2"></i>
                        <span>إدارة الوصفات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= App::pathStartsWith('admin/reports') ? 'active' : '' ?>" 
                       href="<?= App::url('admin/reports') ?>">
                        <i class="bi bi-graph-up"></i>
                        <span>التقارير والإحصائيات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= App::pathStartsWith('admin/notifications') ? 'active' : '' ?>" 
                       href="<?= App::url('admin/notifications') ?>">
                        <i class="bi bi-bell"></i>
                        <span>إدارة الإشعارات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= App::pathStartsWith('admin/activity-log') ? 'active' : '' ?>" 
                       href="<?= App::url('admin/activity-log') ?>">
                        <i class="bi bi-clock-history"></i>
                        <span>سجل النشاطات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= App::pathStartsWith('admin/backup') ? 'active' : '' ?>" 
                       href="<?= App::url('admin/backup') ?>">
                        <i class="bi bi-shield-check"></i>
                        <span>النسخ الاحتياطي</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= App::pathStartsWith('admin/settings') ? 'active' : '' ?>" 
                       href="<?= App::url('admin/settings') ?>">
                        <i class="bi bi-gear"></i>
                        <span>إعدادات النظام</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <div class="text-center text-white-50 small">
                <div class="mb-2">
                    <i class="bi bi-shield-check"></i>
                    HealthKey Admin
                </div>
                <div>الإصدار 1.0</div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Flash Messages -->
            <?php if (SessionHelper::hasFlash()): ?>
                <?php foreach (SessionHelper::getAllFlash() as $key => $flash): ?>
                    <div class="alert alert-<?= $flash['type'] === 'error' ? 'danger' : $flash['type'] ?> alert-dismissible fade show" role="alert">
                        <i class="bi bi-<?= $flash['type'] === 'success' ? 'check-circle' : ($flash['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
                        <?= htmlspecialchars($flash['message']) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- Page Content -->
            <?= $content ?>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-2">جاري التحميل...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?= App::asset('js/admin.js') ?>"></script>
    
    <script>
        // Global functions
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : (type === 'error' ? 'exclamation-triangle' : 'info-circle')} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.main-content .container-fluid');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        // Auto-refresh notifications count
        function updateNotificationsCount() {
            fetch('<?= App::url('api/notifications/count') ?>')
                .then(response => response.json())
                .then(data => {
                    const badge = document.querySelector('.navbar .badge');
                    if (data.count > 0) {
                        if (badge) {
                            badge.textContent = data.count;
                        } else {
                            // Create badge if it doesn't exist
                            const notificationLink = document.getElementById('notificationsDropdown');
                            const newBadge = document.createElement('span');
                            newBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                            newBadge.textContent = data.count;
                            notificationLink.appendChild(newBadge);
                        }
                    } else if (badge) {
                        badge.remove();
                    }
                })
                .catch(error => console.log('Error updating notifications:', error));
        }

        // Update notifications count every 30 seconds
        setInterval(updateNotificationsCount, 30000);

        // Sidebar toggle for mobile
        function toggleSidebar() {
            document.body.classList.toggle('sidebar-open');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggleButton = document.querySelector('.sidebar-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !toggleButton?.contains(event.target)) {
                document.body.classList.remove('sidebar-open');
            }
        });

        // Confirm before leaving page with unsaved changes
        let hasUnsavedChanges = false;

        function markUnsavedChanges() {
            hasUnsavedChanges = true;
        }

        function markSavedChanges() {
            hasUnsavedChanges = false;
        }

        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });

        // Form validation helper
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;

            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
