<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'لوحة تحكم المدير' ?> - HealthKey</title>

    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        .admin-layout {
            padding-top: 76px;
        }
        .sidebar {
            position: fixed;
            top: 76px;
            right: 0;
            width: 250px;
            height: calc(100vh - 76px);
            background: #343a40;
            z-index: 1000;
            overflow-y: auto;
        }
        .main-content {
            margin-right: 250px;
            padding: 20px;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 12px 20px;
            border-radius: 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: #495057;
            color: #fff;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        .loading-spinner {
            text-align: center;
            color: white;
        }
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 0;
            }
        }
    </style>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= UrlHelper::asset('images/favicon.ico') ?>">
</head>
<body class="admin-layout">
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand d-flex align-items-center" href="<?= UrlHelper::url('admin/dashboard') ?>">
                <i class="bi bi-shield-check me-2"></i>
                <span class="fw-bold">HealthKey Admin</span>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Items -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?= UrlHelper::isCurrentPath('admin/dashboard') ? 'active' : '' ?>"
                           href="<?= UrlHelper::url('admin/dashboard') ?>">
                            <i class="bi bi-speedometer2 me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= UrlHelper::pathStartsWith('admin/users') ? 'active' : '' ?>"
                           href="<?= UrlHelper::url('admin/users') ?>">
                            <i class="bi bi-people me-1"></i>
                            المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= UrlHelper::pathStartsWith('admin/appointments') ? 'active' : '' ?>"
                           href="<?= UrlHelper::url('admin/appointments') ?>">
                            <i class="bi bi-calendar-check me-1"></i>
                            المواعيد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= UrlHelper::pathStartsWith('admin/prescriptions') ? 'active' : '' ?>"
                           href="<?= UrlHelper::url('admin/prescriptions') ?>">
                            <i class="bi bi-prescription2 me-1"></i>
                            الوصفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= UrlHelper::pathStartsWith('admin/reports') ? 'active' : '' ?>"
                           href="<?= UrlHelper::url('admin/reports') ?>">
                            <i class="bi bi-graph-up me-1"></i>
                            التقارير
                        </a>
                    </li>
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <!-- Notifications -->
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="#" id="notificationsDropdown"
                           role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-bell"></i>
                            <?php if (SessionHelper::get('unread_notifications', 0) > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    <?= SessionHelper::get('unread_notifications') ?>
                                </span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                            <li><h6 class="dropdown-header">الإشعارات</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?= UrlHelper::url('admin/notifications') ?>">
                                    <i class="bi bi-bell me-2"></i>
                                    عرض جميع الإشعارات
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- User Profile -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#"
                           id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
                                <i class="bi bi-person text-primary"></i>
                            </div>
                            <span><?= SessionHelper::getUserName() ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="<?= UrlHelper::url('admin/profile') ?>">
                                    <i class="bi bi-person-gear me-2"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?= UrlHelper::url('admin/settings') ?>">
                                    <i class="bi bi-gear me-2"></i>
                                    الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="<?= UrlHelper::url('auth/logout') ?>">
                                    <i class="bi bi-box-arrow-right me-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link <?= UrlHelper::isCurrentPath('admin/dashboard') ? 'active' : '' ?>"
                       href="<?= UrlHelper::url('admin/dashboard') ?>">
                        <i class="bi bi-speedometer2 me-2"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= UrlHelper::pathStartsWith('admin/users') ? 'active' : '' ?>"
                       href="<?= UrlHelper::url('admin/users') ?>">
                        <i class="bi bi-people me-2"></i>
                        <span>إدارة المستخدمين</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= UrlHelper::pathStartsWith('admin/appointments') ? 'active' : '' ?>"
                       href="<?= UrlHelper::url('admin/appointments') ?>">
                        <i class="bi bi-calendar-check me-2"></i>
                        <span>إدارة المواعيد</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= UrlHelper::pathStartsWith('admin/prescriptions') ? 'active' : '' ?>"
                       href="<?= UrlHelper::url('admin/prescriptions') ?>">
                        <i class="bi bi-prescription2 me-2"></i>
                        <span>إدارة الوصفات</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= UrlHelper::pathStartsWith('admin/reports') ? 'active' : '' ?>"
                       href="<?= UrlHelper::url('admin/reports') ?>">
                        <i class="bi bi-graph-up me-2"></i>
                        <span>التقارير والإحصائيات</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= UrlHelper::pathStartsWith('admin/notifications') ? 'active' : '' ?>"
                       href="<?= UrlHelper::url('admin/notifications') ?>">
                        <i class="bi bi-bell me-2"></i>
                        <span>إدارة الإشعارات</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= UrlHelper::pathStartsWith('admin/activity-log') ? 'active' : '' ?>"
                       href="<?= UrlHelper::url('admin/activity-log') ?>">
                        <i class="bi bi-clock-history me-2"></i>
                        <span>سجل النشاطات</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= UrlHelper::pathStartsWith('admin/backup') ? 'active' : '' ?>"
                       href="<?= UrlHelper::url('admin/backup') ?>">
                        <i class="bi bi-shield-check me-2"></i>
                        <span>النسخ الاحتياطي</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= UrlHelper::pathStartsWith('admin/settings') ? 'active' : '' ?>"
                       href="<?= UrlHelper::url('admin/settings') ?>">
                        <i class="bi bi-gear me-2"></i>
                        <span>إعدادات النظام</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Flash Messages -->
            <?php if (SessionHelper::hasFlash()): ?>
                <?php foreach (SessionHelper::getAllFlash() as $key => $flash): ?>
                    <div class="alert alert-<?= $flash['type'] === 'error' ? 'danger' : $flash['type'] ?> alert-dismissible fade show" role="alert">
                        <i class="bi bi-<?= $flash['type'] === 'success' ? 'check-circle' : ($flash['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
                        <?= htmlspecialchars($flash['message']) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- Page Content -->
            <?= $content ?>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-2">جاري التحميل...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
</script>
    
    <script>
        // Global functions
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : (type === 'error' ? 'exclamation-triangle' : 'info-circle')} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.main-content .container-fluid');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggleButton = document.querySelector('.navbar-toggler');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggleButton?.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });

        // Auto-refresh notifications count
        function updateNotificationsCount() {
            fetch('<?= App::url('api/notifications/count') ?>')
                .then(response => response.json())
                .then(data => {
                    const badge = document.querySelector('.navbar .badge');
                    if (data.count > 0) {
                        if (badge) {
                            badge.textContent = data.count;
                        } else {
                            // Create badge if it doesn't exist
                            const notificationLink = document.getElementById('notificationsDropdown');
                            const newBadge = document.createElement('span');
                            newBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                            newBadge.textContent = data.count;
                            notificationLink.appendChild(newBadge);
                        }
                    } else if (badge) {
                        badge.remove();
                    }
                })
                .catch(error => console.log('Error updating notifications:', error));
        }

        // Update notifications count every 30 seconds
        setInterval(updateNotificationsCount, 30000);

        // Sidebar toggle for mobile
        function toggleSidebar() {
            document.body.classList.toggle('sidebar-open');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggleButton = document.querySelector('.sidebar-toggle');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !toggleButton?.contains(event.target)) {
                document.body.classList.remove('sidebar-open');
            }
        });

        // Confirm before leaving page with unsaved changes
        let hasUnsavedChanges = false;

        function markUnsavedChanges() {
            hasUnsavedChanges = true;
        }

        function markSavedChanges() {
            hasUnsavedChanges = false;
        }

        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });

        // Form validation helper
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;

            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
