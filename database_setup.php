<?php
/**
 * ملف إعداد قاعدة البيانات
 * يقوم بإنشاء قاعدة البيانات والجداول تلقائياً
 */

require_once 'config.php';

echo "<h2>إعداد قاعدة البيانات - HealthKey</h2>";
echo "<hr>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "<h3>1. إنشاء قاعدة البيانات:</h3>";
    
    // إنشاء قاعدة البيانات
    $createDbQuery = "CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $pdo->exec($createDbQuery);
    echo "✅ تم إنشاء قاعدة البيانات '" . DB_NAME . "' بنجاح<br><br>";

    // الاتصال بقاعدة البيانات المحددة
    $pdo->exec("USE " . DB_NAME);

    echo "<h3>2. إنشاء الجداول:</h3>";

    // قراءة ملف SQL وتنفيذه
    $sqlFile = 'database_schema.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql);
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^(CREATE DATABASE|USE)/i', $query)) {
                try {
                    $pdo->exec($query);
                } catch (PDOException $e) {
                    // تجاهل أخطاء الجداول الموجودة مسبقاً
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "⚠️ تحذير في الاستعلام: " . substr($query, 0, 50) . "...<br>";
                        echo "الخطأ: " . $e->getMessage() . "<br>";
                    }
                }
            }
        }
        echo "✅ تم إنشاء جميع الجداول بنجاح<br><br>";
    } else {
        echo "❌ ملف database_schema.sql غير موجود<br><br>";
    }

    echo "<h3>3. التحقق من الجداول:</h3>";
    
    // فحص الجداول المنشأة
    $tablesQuery = "SHOW TABLES";
    $stmt = $pdo->query($tablesQuery);
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($tables)) {
        echo "✅ الجداول المنشأة (" . count($tables) . " جدول):<br>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul><br>";
    } else {
        echo "❌ لم يتم العثور على أي جداول<br><br>";
    }

    echo "<h3>4. التحقق من البيانات التجريبية:</h3>";
    
    // فحص المستخدمين التجريبيين
    $usersQuery = "SELECT id, email, user_type, first_name, last_name FROM users";
    $stmt = $pdo->query($usersQuery);
    $users = $stmt->fetchAll();
    
    if (!empty($users)) {
        echo "✅ المستخدمون التجريبيون (" . count($users) . " مستخدم):<br>";
        echo "<table border='1' style='border-collapse: collapse; margin-top: 10px;'>";
        echo "<tr><th>ID</th><th>البريد الإلكتروني</th><th>النوع</th><th>الاسم</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['user_type'] . "</td>";
            echo "<td>" . $user['first_name'] . " " . $user['last_name'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    } else {
        echo "⚠️ لا توجد بيانات تجريبية<br><br>";
    }

    echo "<h3>✅ تم إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<p><strong>الخطوة التالية:</strong> يمكنك الآن تشغيل <a href='test_database.php'>test_database.php</a> لاختبار الاتصال</p>";
    
    echo "<hr>";
    echo "<h4>معلومات تسجيل الدخول التجريبية:</h4>";
    echo "<ul>";
    echo "<li><strong>المدير:</strong> <EMAIL> / password</li>";
    echo "<li><strong>الطبيب:</strong> <EMAIL> / password</li>";
    echo "<li><strong>الصيدلي:</strong> <EMAIL> / password</li>";
    echo "<li><strong>المريض:</strong> <EMAIL> / password</li>";
    echo "</ul>";

} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ في إعداد قاعدة البيانات:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<br>";
    echo "<h4>خطوات حل المشكلة:</h4>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تحقق من إعدادات قاعدة البيانات في ملف config.php</li>";
    echo "<li>تأكد من صلاحيات المستخدم لإنشاء قواعد البيانات</li>";
    echo "</ol>";
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ خطأ عام:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
