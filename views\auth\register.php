<div class="row justify-content-center">
    <div class="col-md-8">
        <h2 class="text-center mb-4">
            <i class="bi bi-person-plus me-2"></i>
            إنشاء حساب جديد
        </h2>
        
        <form method="POST" action="<?= App::url('register') ?>" data-validate="true">
            <!-- اختيار نوع المستخدم -->
            <div class="mb-4">
                <label class="form-label">
                    <i class="bi bi-person-badge me-2"></i>
                    نوع المستخدم
                </label>
                <div class="user-type-cards">
                    <div class="user-type-card" data-type="patient">
                        <i class="bi bi-person"></i>
                        <h6>مريض</h6>
                        <p>للمرضى الذين يريدون إدارة سجلهم الطبي</p>
                    </div>
                    <div class="user-type-card" data-type="doctor">
                        <i class="bi bi-person-badge"></i>
                        <h6>طبيب</h6>
                        <p>للأطباء لإدارة المرضى والوصفات</p>
                    </div>
                    <div class="user-type-card" data-type="pharmacist">
                        <i class="bi bi-capsule"></i>
                        <h6>صيدلي</h6>
                        <p>للصيادلة للتحقق من الوصفات</p>
                    </div>
                </div>
                <input type="hidden" name="user_type" value="<?= htmlspecialchars($old_input['user_type'] ?? '') ?>" required>
                <?php if (isset($errors['user_type'])): ?>
                    <div class="invalid-feedback d-block">
                        <?= htmlspecialchars($errors['user_type']) ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- المعلومات الشخصية -->
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="first_name" class="form-label">الاسم الأول</label>
                    <input 
                        type="text" 
                        class="form-control <?= isset($errors['first_name']) ? 'is-invalid' : '' ?>" 
                        id="first_name" 
                        name="first_name" 
                        value="<?= htmlspecialchars($old_input['first_name'] ?? '') ?>"
                        required
                        placeholder="الاسم الأول"
                    >
                    <?php if (isset($errors['first_name'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($errors['first_name']) ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="last_name" class="form-label">الاسم الأخير</label>
                    <input 
                        type="text" 
                        class="form-control <?= isset($errors['last_name']) ? 'is-invalid' : '' ?>" 
                        id="last_name" 
                        name="last_name" 
                        value="<?= htmlspecialchars($old_input['last_name'] ?? '') ?>"
                        required
                        placeholder="الاسم الأخير"
                    >
                    <?php if (isset($errors['last_name'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($errors['last_name']) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <input 
                        type="email" 
                        class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                        id="email" 
                        name="email" 
                        value="<?= htmlspecialchars($old_input['email'] ?? '') ?>"
                        required
                        placeholder="<EMAIL>"
                    >
                    <?php if (isset($errors['email'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($errors['email']) ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <input 
                        type="tel" 
                        class="form-control <?= isset($errors['phone']) ? 'is-invalid' : '' ?>" 
                        id="phone" 
                        name="phone" 
                        value="<?= htmlspecialchars($old_input['phone'] ?? '') ?>"
                        required
                        placeholder="05xxxxxxxx"
                    >
                    <?php if (isset($errors['phone'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($errors['phone']) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="password-field">
                        <input 
                            type="password" 
                            class="form-control <?= isset($errors['password']) ? 'is-invalid' : '' ?>" 
                            id="password" 
                            name="password" 
                            required
                            placeholder="8 أحرف على الأقل"
                        >
                        <button type="button" class="password-toggle">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                    <?php if (isset($errors['password'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($errors['password']) ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                    <div class="password-field">
                        <input 
                            type="password" 
                            class="form-control <?= isset($errors['confirm_password']) ? 'is-invalid' : '' ?>" 
                            id="confirm_password" 
                            name="confirm_password" 
                            required
                            placeholder="أعد كتابة كلمة المرور"
                        >
                        <button type="button" class="password-toggle">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                    <?php if (isset($errors['confirm_password'])): ?>
                        <div class="invalid-feedback">
                            <?= htmlspecialchars($errors['confirm_password']) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- معلومات إضافية اختيارية -->
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="date_of_birth" class="form-label">تاريخ الميلاد (اختياري)</label>
                    <input 
                        type="date" 
                        class="form-control" 
                        id="date_of_birth" 
                        name="date_of_birth" 
                        value="<?= htmlspecialchars($old_input['date_of_birth'] ?? '') ?>"
                    >
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="gender" class="form-label">الجنس (اختياري)</label>
                    <select class="form-select" id="gender" name="gender">
                        <option value="">اختر الجنس</option>
                        <option value="male" <?= ($old_input['gender'] ?? '') === 'male' ? 'selected' : '' ?>>ذكر</option>
                        <option value="female" <?= ($old_input['gender'] ?? '') === 'female' ? 'selected' : '' ?>>أنثى</option>
                    </select>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="national_id" class="form-label">الرقم الوطني (اختياري)</label>
                <input 
                    type="text" 
                    class="form-control <?= isset($errors['national_id']) ? 'is-invalid' : '' ?>" 
                    id="national_id" 
                    name="national_id" 
                    value="<?= htmlspecialchars($old_input['national_id'] ?? '') ?>"
                    placeholder="الرقم الوطني"
                >
                <?php if (isset($errors['national_id'])): ?>
                    <div class="invalid-feedback">
                        <?= htmlspecialchars($errors['national_id']) ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="mb-3">
                <label for="address" class="form-label">العنوان (اختياري)</label>
                <textarea 
                    class="form-control" 
                    id="address" 
                    name="address" 
                    rows="2"
                    placeholder="العنوان الكامل"
                ><?= htmlspecialchars($old_input['address'] ?? '') ?></textarea>
            </div>
            
            <!-- حقول إضافية للأطباء -->
            <div class="additional-fields doctor-fields" style="display: none;">
                <h5 class="text-primary mb-3">معلومات الطبيب</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="license_number" class="form-label">رقم الترخيص</label>
                        <input 
                            type="text" 
                            class="form-control" 
                            id="license_number" 
                            name="license_number" 
                            value="<?= htmlspecialchars($old_input['license_number'] ?? '') ?>"
                            placeholder="رقم ترخيص مزاولة المهنة"
                        >
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="specialization" class="form-label">التخصص</label>
                        <input 
                            type="text" 
                            class="form-control" 
                            id="specialization" 
                            name="specialization" 
                            value="<?= htmlspecialchars($old_input['specialization'] ?? '') ?>"
                            placeholder="التخصص الطبي"
                        >
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="years_of_experience" class="form-label">سنوات الخبرة</label>
                    <input 
                        type="number" 
                        class="form-control" 
                        id="years_of_experience" 
                        name="years_of_experience" 
                        value="<?= htmlspecialchars($old_input['years_of_experience'] ?? '') ?>"
                        min="0"
                        placeholder="عدد سنوات الخبرة"
                    >
                </div>
            </div>
            
            <!-- حقول إضافية للصيادلة -->
            <div class="additional-fields pharmacist-fields" style="display: none;">
                <h5 class="text-info mb-3">معلومات الصيدلي</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="pharmacist_license_number" class="form-label">رقم الترخيص</label>
                        <input 
                            type="text" 
                            class="form-control" 
                            id="pharmacist_license_number" 
                            name="license_number" 
                            value="<?= htmlspecialchars($old_input['license_number'] ?? '') ?>"
                            placeholder="رقم ترخيص مزاولة المهنة"
                        >
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="pharmacy_name" class="form-label">اسم الصيدلية</label>
                        <input 
                            type="text" 
                            class="form-control" 
                            id="pharmacy_name" 
                            name="pharmacy_name" 
                            value="<?= htmlspecialchars($old_input['pharmacy_name'] ?? '') ?>"
                            placeholder="اسم الصيدلية"
                        >
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="pharmacy_address" class="form-label">عنوان الصيدلية</label>
                    <textarea 
                        class="form-control" 
                        id="pharmacy_address" 
                        name="pharmacy_address" 
                        rows="2"
                        placeholder="العنوان الكامل للصيدلية"
                    ><?= htmlspecialchars($old_input['pharmacy_address'] ?? '') ?></textarea>
                </div>
            </div>
            
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-person-plus me-2"></i>
                    إنشاء الحساب
                </button>
            </div>
        </form>
        
        <div class="auth-links">
            <p class="mb-2">لديك حساب بالفعل؟</p>
            <a href="<?= App::url('login') ?>" class="btn btn-outline-primary">
                <i class="bi bi-box-arrow-in-right me-2"></i>
                تسجيل الدخول
            </a>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?= App::url() ?>" class="text-muted">
                <i class="bi bi-arrow-right me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
    </div>
</div>
