<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * يستخدم لاختبار إعدادات قاعدة البيانات والتأكد من عمل الاتصال
 */

// تضمين ملف الإعدادات
require_once 'config.php';
require_once 'app/core/Database.php';

echo "<h2>اختبار الاتصال بقاعدة البيانات - HealthKey</h2>";
echo "<hr>";

try {
    // اختبار الاتصال بقاعدة البيانات
    echo "<h3>1. اختبار الاتصال الأساسي:</h3>";
    $db = Database::getInstance();
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح!<br><br>";

    // اختبار وجود الجداول الأساسية
    echo "<h3>2. فحص الجداول الأساسية:</h3>";
    $tables = ['users', 'doctors', 'pharmacists', 'appointments', 'prescriptions', 'medical_records'];
    
    foreach ($tables as $table) {
        if ($db->tableExists($table)) {
            echo "✅ الجدول '$table' موجود<br>";
        } else {
            echo "❌ الجدول '$table' غير موجود<br>";
        }
    }
    echo "<br>";

    // اختبار جلب البيانات
    echo "<h3>3. اختبار جلب البيانات:</h3>";
    $users = $db->select("SELECT id, email, user_type, first_name, last_name FROM users LIMIT 5");
    
    if (!empty($users)) {
        echo "✅ تم جلب البيانات بنجاح. عدد المستخدمين: " . count($users) . "<br>";
        echo "<table border='1' style='border-collapse: collapse; margin-top: 10px;'>";
        echo "<tr><th>ID</th><th>البريد الإلكتروني</th><th>نوع المستخدم</th><th>الاسم الأول</th><th>الاسم الأخير</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['user_type']) . "</td>";
            echo "<td>" . htmlspecialchars($user['first_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['last_name']) . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    } else {
        echo "⚠️ لا توجد بيانات في جدول المستخدمين<br><br>";
    }

    // اختبار العمليات الأساسية
    echo "<h3>4. اختبار العمليات الأساسية:</h3>";
    
    // اختبار INSERT
    $testData = [
        ':email' => 'test_' . time() . '@example.com',
        ':password' => password_hash('test123', PASSWORD_DEFAULT),
        ':user_type' => 'patient',
        ':first_name' => 'مستخدم',
        ':last_name' => 'تجريبي'
    ];
    
    $insertQuery = "INSERT INTO users (email, password, user_type, first_name, last_name) 
                    VALUES (:email, :password, :user_type, :first_name, :last_name)";
    
    $insertId = $db->insert($insertQuery, $testData);
    
    if ($insertId) {
        echo "✅ تم إدراج مستخدم تجريبي بنجاح. ID: $insertId<br>";
        
        // اختبار UPDATE
        $updateQuery = "UPDATE users SET phone = :phone WHERE id = :id";
        $updateParams = [':phone' => '**********', ':id' => $insertId];
        $updatedRows = $db->update($updateQuery, $updateParams);
        
        if ($updatedRows > 0) {
            echo "✅ تم تحديث بيانات المستخدم بنجاح<br>";
        } else {
            echo "⚠️ لم يتم تحديث أي بيانات<br>";
        }
        
        // اختبار SELECT للمستخدم الجديد
        $selectQuery = "SELECT * FROM users WHERE id = :id";
        $user = $db->selectOne($selectQuery, [':id' => $insertId]);
        
        if ($user) {
            echo "✅ تم جلب بيانات المستخدم الجديد بنجاح<br>";
        }
        
        // اختبار DELETE
        $deleteQuery = "DELETE FROM users WHERE id = :id";
        $deletedRows = $db->delete($deleteQuery, [':id' => $insertId]);
        
        if ($deletedRows > 0) {
            echo "✅ تم حذف المستخدم التجريبي بنجاح<br>";
        } else {
            echo "⚠️ لم يتم حذف أي مستخدم<br>";
        }
    } else {
        echo "❌ فشل في إدراج المستخدم التجريبي<br>";
    }
    
    echo "<br>";

    // اختبار المعاملات (Transactions)
    echo "<h3>5. اختبار المعاملات:</h3>";
    
    if ($db->beginTransaction()) {
        echo "✅ تم بدء المعاملة بنجاح<br>";
        
        // محاولة إدراج بيانات
        $transactionTest = $db->insert($insertQuery, [
            ':email' => 'transaction_test_' . time() . '@example.com',
            ':password' => password_hash('test123', PASSWORD_DEFAULT),
            ':user_type' => 'patient',
            ':first_name' => 'اختبار',
            ':last_name' => 'المعاملة'
        ]);
        
        if ($transactionTest) {
            echo "✅ تم إدراج البيانات في المعاملة<br>";
            
            if ($db->rollback()) {
                echo "✅ تم إلغاء المعاملة بنجاح (Rollback)<br>";
            } else {
                echo "❌ فشل في إلغاء المعاملة<br>";
            }
        } else {
            echo "❌ فشل في إدراج البيانات في المعاملة<br>";
            $db->rollback();
        }
    } else {
        echo "❌ فشل في بدء المعاملة<br>";
    }

    echo "<br><h3>✅ جميع الاختبارات اكتملت بنجاح!</h3>";
    echo "<p><strong>ملاحظة:</strong> إذا ظهرت أي أخطاء أعلاه، يرجى التحقق من:</p>";
    echo "<ul>";
    echo "<li>إعدادات قاعدة البيانات في ملف config.php</li>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>وجود قاعدة البيانات والجداول (تشغيل ملف database_schema.sql)</li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات:</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<br>";
    echo "<h4>خطوات حل المشكلة:</h4>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تحقق من إعدادات قاعدة البيانات في ملف config.php</li>";
    echo "<li>تأكد من وجود قاعدة البيانات 'healthkey'</li>";
    echo "<li>قم بتشغيل ملف database_schema.sql لإنشاء الجداول</li>";
    echo "</ol>";
}
?>
