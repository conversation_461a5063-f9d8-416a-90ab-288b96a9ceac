<?php

/**
 * متحكم المريض
 * يعالج جميع العمليات المتعلقة بالمرضى
 */
class PatientController extends Controller
{
    private $userModel;
    private $appointmentModel;
    private $prescriptionModel;
    private $medicalRecordModel;
    private $notificationModel;

    public function __construct()
    {
        parent::__construct();
        
        // التحقق من تسجيل الدخول ونوع المستخدم
        $this->requireAuth();
        $this->requireUserType('patient');
        
        // تحميل النماذج
        $this->userModel = new User();
        $this->appointmentModel = new Appointment();
        $this->prescriptionModel = new Prescription();
        $this->medicalRecordModel = new MedicalRecord();
        $this->notificationModel = new Notification();
    }

    /**
     * لوحة تحكم المريض
     */
    public function dashboard()
    {
        $patientId = $this->currentUser['id'];
        
        // الحصول على البيانات للوحة التحكم
        $data = [
            'title' => 'لوحة تحكم المريض',
            'patient' => $this->currentUser,
            'upcomingAppointments' => $this->appointmentModel->getUpcoming($patientId, 'patient', 5),
            'recentPrescriptions' => $this->prescriptionModel->getByPatient($patientId, 5),
            'notifications' => $this->notificationModel->getByUser($patientId, false, 5),
            'unreadNotifications' => $this->notificationModel->getUnreadCount($patientId),
            'medicalSummary' => $this->medicalRecordModel->getPatientSummary($patientId)
        ];

        $this->view('patient/dashboard', $data);
    }

    /**
     * الملف الشخصي
     */
    public function profile()
    {
        $patientId = $this->currentUser['id'];
        
        if (App::isPost()) {
            $this->updateProfile();
            return;
        }

        $data = [
            'title' => 'الملف الشخصي',
            'patient' => $this->userModel->findById($patientId),
            'allergies' => $this->medicalRecordModel->getAllergies($patientId)
        ];

        $this->view('patient/profile', $data);
    }

    /**
     * تحديث الملف الشخصي
     */
    private function updateProfile()
    {
        $patientId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->validate($postData, [
            'first_name' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 50
            ],
            'last_name' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 50
            ],
            'phone' => [
                'required' => true,
                'phone' => true
            ],
            'email' => [
                'required' => true,
                'email' => true
            ]
        ]);

        // التحقق من تفرد البريد الإلكتروني
        if (empty($errors['email'])) {
            $existingUser = $this->userModel->findByEmail($postData['email']);
            if ($existingUser && $existingUser['id'] != $patientId) {
                $errors['email'] = 'البريد الإلكتروني مستخدم بالفعل';
            }
        }

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('patient/profile');
            return;
        }

        // تحديث البيانات
        $updateData = [
            'first_name' => $postData['first_name'],
            'last_name' => $postData['last_name'],
            'phone' => $postData['phone'],
            'email' => $postData['email'],
            'date_of_birth' => $postData['date_of_birth'] ?? null,
            'gender' => $postData['gender'] ?? null,
            'address' => $postData['address'] ?? null
        ];

        if ($this->userModel->update($patientId, $updateData)) {
            // تحديث بيانات الجلسة
            $_SESSION['user_name'] = $postData['first_name'] . ' ' . $postData['last_name'];
            
            $this->setFlashMessage('تم تحديث الملف الشخصي بنجاح', 'success');
        } else {
            $this->setFlashMessage('حدث خطأ أثناء تحديث الملف الشخصي', 'error');
        }

        $this->redirect('patient/profile');
    }

    /**
     * المواعيد
     */
    public function appointments()
    {
        $patientId = $this->currentUser['id'];
        $status = App::get('status');
        
        $data = [
            'title' => 'مواعيدي',
            'appointments' => $this->appointmentModel->getByPatient($patientId, $status),
            'currentStatus' => $status,
            'doctors' => $this->userModel->getActiveDoctors()
        ];

        $this->view('patient/appointments', $data);
    }

    /**
     * حجز موعد جديد
     */
    public function bookAppointment()
    {
        if (App::isPost()) {
            $this->processBookAppointment();
            return;
        }

        $data = [
            'title' => 'حجز موعد جديد',
            'doctors' => $this->userModel->getActiveDoctors()
        ];

        $this->view('patient/book_appointment', $data);
    }

    /**
     * معالجة حجز الموعد
     */
    private function processBookAppointment()
    {
        $patientId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->appointmentModel->validate(array_merge($postData, ['patient_id' => $patientId]));

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('patient/book-appointment');
            return;
        }

        // إنشاء الموعد
        $appointmentData = [
            'patient_id' => $patientId,
            'doctor_id' => $postData['doctor_id'],
            'appointment_date' => $postData['appointment_date'],
            'appointment_time' => $postData['appointment_time'],
            'reason' => $postData['reason'] ?? null,
            'status' => 'scheduled'
        ];

        $appointmentId = $this->appointmentModel->create($appointmentData);

        if ($appointmentId) {
            // إرسال إشعار
            $this->notificationModel->notifyNewAppointment(
                $patientId,
                $postData['doctor_id'],
                $postData['appointment_date'],
                $postData['appointment_time']
            );

            $this->setFlashMessage('تم حجز الموعد بنجاح', 'success');
            $this->redirect('patient/appointments');
        } else {
            $this->setFlashMessage('فشل في حجز الموعد. يرجى المحاولة مرة أخرى', 'error');
            $this->redirect('patient/book-appointment');
        }
    }

    /**
     * إلغاء موعد
     */
    public function cancelAppointment()
    {
        $appointmentId = App::post('appointment_id');
        $reason = App::post('reason', 'إلغاء من قبل المريض');
        
        if (!$appointmentId) {
            $this->json(['success' => false, 'message' => 'معرف الموعد مطلوب'], 400);
            return;
        }

        // التحقق من ملكية الموعد
        $appointment = $this->appointmentModel->findById($appointmentId);
        if (!$appointment || $appointment['patient_id'] != $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'الموعد غير موجود'], 404);
            return;
        }

        if ($this->appointmentModel->cancel($appointmentId, $reason)) {
            // إرسال إشعار للطبيب
            $this->notificationModel->notifyAppointmentCancellation(
                $appointment['patient_id'],
                $appointment['doctor_id'],
                $appointment['appointment_date'],
                $appointment['appointment_time']
            );

            $this->json(['success' => true, 'message' => 'تم إلغاء الموعد بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في إلغاء الموعد'], 500);
        }
    }

    /**
     * الوصفات الطبية
     */
    public function prescriptions()
    {
        $patientId = $this->currentUser['id'];
        
        $data = [
            'title' => 'وصفاتي الطبية',
            'prescriptions' => $this->prescriptionModel->getByPatient($patientId)
        ];

        $this->view('patient/prescriptions', $data);
    }

    /**
     * عرض تفاصيل الوصفة
     */
    public function viewPrescription($prescriptionId)
    {
        // التحقق من ملكية الوصفة
        $prescription = $this->prescriptionModel->findById($prescriptionId);
        if (!$prescription || $prescription['patient_id'] != $this->currentUser['id']) {
            $this->setFlashMessage('الوصفة غير موجودة', 'error');
            $this->redirect('patient/prescriptions');
            return;
        }

        $data = [
            'title' => 'تفاصيل الوصفة',
            'prescription' => $prescription,
            'medications' => $this->prescriptionModel->getMedications($prescriptionId)
        ];

        $this->view('patient/prescription_details', $data);
    }

    /**
     * السجل الطبي
     */
    public function medicalRecord()
    {
        $patientId = $this->currentUser['id'];
        
        $data = [
            'title' => 'سجلي الطبي',
            'medicalRecords' => $this->medicalRecordModel->getByPatient($patientId),
            'allergies' => $this->medicalRecordModel->getAllergies($patientId),
            'labTests' => $this->medicalRecordModel->getLabTests($patientId),
            'summary' => $this->medicalRecordModel->getPatientSummary($patientId)
        ];

        $this->view('patient/medical_record', $data);
    }

    /**
     * إضافة حساسية
     */
    public function addAllergy()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $patientId = $this->currentUser['id'];
        $allergen = App::post('allergen');
        $reaction = App::post('reaction');
        $severity = App::post('severity', 'mild');
        $notes = App::post('notes');

        if (empty($allergen)) {
            $this->json(['success' => false, 'message' => 'اسم المادة المسببة للحساسية مطلوب'], 400);
            return;
        }

        $allergyId = $this->medicalRecordModel->addAllergy($patientId, $allergen, $reaction, $severity, $notes);

        if ($allergyId) {
            $this->json(['success' => true, 'message' => 'تم إضافة الحساسية بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في إضافة الحساسية'], 500);
        }
    }

    /**
     * الإشعارات
     */
    public function notifications()
    {
        $patientId = $this->currentUser['id'];
        $page = (int)App::get('page', 1);
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        $data = [
            'title' => 'الإشعارات',
            'notifications' => $this->notificationModel->getByUser($patientId, false, $limit),
            'unreadCount' => $this->notificationModel->getUnreadCount($patientId)
        ];

        $this->view('patient/notifications', $data);
    }

    /**
     * تسجيل الإشعار كمقروء
     */
    public function markNotificationRead()
    {
        $notificationId = App::post('notification_id');
        
        if (!$notificationId) {
            $this->json(['success' => false, 'message' => 'معرف الإشعار مطلوب'], 400);
            return;
        }

        // التحقق من ملكية الإشعار
        $notification = $this->notificationModel->findById($notificationId);
        if (!$notification || $notification['user_id'] != $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'الإشعار غير موجود'], 404);
            return;
        }

        if ($this->notificationModel->markAsRead($notificationId)) {
            $this->json(['success' => true, 'message' => 'تم تسجيل الإشعار كمقروء']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تحديث الإشعار'], 500);
        }
    }

    /**
     * تسجيل جميع الإشعارات كمقروءة
     */
    public function markAllNotificationsRead()
    {
        $patientId = $this->currentUser['id'];
        
        $count = $this->notificationModel->markAllAsRead($patientId);
        
        $this->json([
            'success' => true, 
            'message' => "تم تسجيل $count إشعار كمقروء",
            'count' => $count
        ]);
    }

    /**
     * البحث في الأطباء
     */
    public function searchDoctors()
    {
        $query = App::get('q', '');
        $specialization = App::get('specialization', '');
        
        if (empty($query) && empty($specialization)) {
            $this->json(['doctors' => []]);
            return;
        }

        $doctors = $this->userModel->search($query, 'doctor');
        
        // تصفية حسب التخصص إذا تم تحديده
        if (!empty($specialization)) {
            $doctors = array_filter($doctors, function($doctor) use ($specialization) {
                return stripos($doctor['specialization'], $specialization) !== false;
            });
        }

        $this->json(['doctors' => array_values($doctors)]);
    }

    /**
     * الحصول على الأوقات المتاحة للطبيب
     */
    public function getAvailableSlots()
    {
        $doctorId = App::get('doctor_id');
        $date = App::get('date');
        
        if (!$doctorId || !$date) {
            $this->json(['slots' => []], 400);
            return;
        }

        $availableSlots = $this->appointmentModel->getAvailableTimeSlots($doctorId, $date);
        
        $this->json(['slots' => $availableSlots]);
    }

    /**
     * تغيير كلمة المرور
     */
    public function changePassword()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $patientId = $this->currentUser['id'];
        $currentPassword = App::post('current_password');
        $newPassword = App::post('new_password');
        $confirmPassword = App::post('confirm_password');

        // التحقق من كلمة المرور الحالية
        if (!$this->userModel->verifyPassword($patientId, $currentPassword)) {
            $this->json(['success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة'], 400);
            return;
        }

        // التحقق من تطابق كلمة المرور الجديدة
        if ($newPassword !== $confirmPassword) {
            $this->json(['success' => false, 'message' => 'كلمات المرور الجديدة غير متطابقة'], 400);
            return;
        }

        // التحقق من قوة كلمة المرور
        $passwordErrors = ValidationHelper::validatePasswordStrength($newPassword);
        if (!empty($passwordErrors)) {
            $this->json(['success' => false, 'message' => implode(', ', $passwordErrors)], 400);
            return;
        }

        if ($this->userModel->updatePassword($patientId, $newPassword)) {
            $this->json(['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تغيير كلمة المرور'], 500);
        }
    }
}
