<?php

/**
 * فئة إدارة قاعدة البيانات
 * مسؤولة عن إنشاء وإدارة الاتصال بقاعدة البيانات باستخدام PDO
 */
class Database
{
    private static $instance = null;
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;

    /**
     * منشئ الفئة - يتم استدعاؤه مرة واحدة فقط (Singleton Pattern)
     */
    private function __construct()
    {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;

        $this->connect();
    }

    /**
     * الحصول على مثيل وحيد من فئة قاعدة البيانات
     * @return Database
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * إنشاء الاتصال بقاعدة البيانات
     */
    private function connect()
    {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}"
            ];

            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch (PDOException $e) {
            $this->handleError("فشل في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }

    /**
     * الحصول على اتصال قاعدة البيانات
     * @return PDO
     */
    public function getConnection()
    {
        return $this->connection;
    }

    /**
     * تنفيذ استعلام SELECT
     * @param string $query الاستعلام
     * @param array $params المعاملات
     * @return array
     */
    public function select($query, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            $this->handleError("خطأ في استعلام SELECT: " . $e->getMessage());
            return [];
        }
    }

    /**
     * تنفيذ استعلام SELECT لجلب سجل واحد
     * @param string $query الاستعلام
     * @param array $params المعاملات
     * @return array|false
     */
    public function selectOne($query, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            $this->handleError("خطأ في استعلام SELECT: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تنفيذ استعلام INSERT
     * @param string $query الاستعلام
     * @param array $params المعاملات
     * @return int|false آخر ID تم إدراجه أو false في حالة الفشل
     */
    public function insert($query, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            
            if ($result) {
                return $this->connection->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            $this->handleError("خطأ في استعلام INSERT: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تنفيذ استعلام UPDATE
     * @param string $query الاستعلام
     * @param array $params المعاملات
     * @return int عدد الصفوف المتأثرة
     */
    public function update($query, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->handleError("خطأ في استعلام UPDATE: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * تنفيذ استعلام DELETE
     * @param string $query الاستعلام
     * @param array $params المعاملات
     * @return int عدد الصفوف المحذوفة
     */
    public function delete($query, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->handleError("خطأ في استعلام DELETE: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * بدء معاملة قاعدة البيانات
     * @return bool
     */
    public function beginTransaction()
    {
        try {
            return $this->connection->beginTransaction();
        } catch (PDOException $e) {
            $this->handleError("خطأ في بدء المعاملة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تأكيد المعاملة
     * @return bool
     */
    public function commit()
    {
        try {
            return $this->connection->commit();
        } catch (PDOException $e) {
            $this->handleError("خطأ في تأكيد المعاملة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إلغاء المعاملة
     * @return bool
     */
    public function rollback()
    {
        try {
            return $this->connection->rollback();
        } catch (PDOException $e) {
            $this->handleError("خطأ في إلغاء المعاملة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من وجود جدول في قاعدة البيانات
     * @param string $tableName اسم الجدول
     * @return bool
     */
    public function tableExists($tableName)
    {
        try {
            $query = "SHOW TABLES LIKE :tableName";
            $result = $this->selectOne($query, [':tableName' => $tableName]);
            return $result !== false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * معالجة الأخطاء
     * @param string $message رسالة الخطأ
     */
    private function handleError($message)
    {
        // تسجيل الخطأ في ملف السجل
        error_log($message);
        
        // في بيئة التطوير، عرض الخطأ
        if (defined('APP_DEBUG') && APP_DEBUG) {
            throw new Exception($message);
        }
    }

    /**
     * منع استنساخ الكائن
     */
    private function __clone() {}

    /**
     * منع إلغاء تسلسل الكائن
     */
    public function __wakeup()
    {
        throw new Exception("Cannot unserialize singleton");
    }
}
