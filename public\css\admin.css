/* Admin Layout Styles */
.admin-layout {
    padding-top: 76px;
    background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 76px;
    right: 0;
    width: 250px;
    height: calc(100vh - 76px);
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    z-index: 1000;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #495057;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 3px;
}

.sidebar .nav-link {
    color: #adb5bd;
    padding: 15px 20px;
    border-radius: 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: #fff;
    transform: translateX(-5px);
}

.sidebar .nav-link.active {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    color: #fff;
    border-left: 4px solid #ffc107;
}

.sidebar .nav-link i {
    font-size: 1.1rem;
    width: 20px;
}

/* Main Content */
.main-content {
    margin-right: 250px;
    padding: 30px;
    min-height: calc(100vh - 76px);
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 25px rgba(0,0,0,0.12);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
    padding: 20px;
}

.card-body {
    padding: 25px;
}

/* Statistics Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%) !important;
    border: none;
    overflow: hidden;
    position: relative;
}

.card.bg-success {
    background: linear-gradient(135deg, var(--bs-success) 0%, #0f5132 100%) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, var(--bs-info) 0%, #055160 100%) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-warning) 0%, #664d03 100%) !important;
}

.card.bg-primary::before,
.card.bg-success::before,
.card.bg-info::before,
.card.bg-warning::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

/* Buttons */
.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
}

/* Tables */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-color: #f1f3f4;
}

/* Badges */
.badge {
    padding: 8px 12px;
    font-size: 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

/* Avatars */
.avatar-sm {
    width: 35px;
    height: 35px;
}

.avatar-md {
    width: 50px;
    height: 50px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
    border-left: 4px solid;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left-color: #28a745;
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-left-color: #dc3545;
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left-color: #ffc107;
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-left-color: #17a2b8;
    color: #0c5460;
}

/* Navbar */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar .nav-link:hover {
    transform: translateY(-1px);
}

/* Dropdown */
.dropdown-menu {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    padding: 10px 0;
}

.dropdown-item {
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateX(5px);
}

/* Pagination */
.pagination .page-link {
    border: none;
    border-radius: 8px;
    margin: 0 2px;
    padding: 10px 15px;
    color: #6c757d;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: translateY(-1px);
}

.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 280px;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        padding: 20px 15px;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .table-responsive {
        border-radius: 8px;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 15px 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.875rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 2px 20px rgba(0,0,0,0.08) !important;
}

.shadow-hover {
    transition: all 0.3s ease;
}

.shadow-hover:hover {
    box-shadow: 0 4px 25px rgba(0,0,0,0.15) !important;
    transform: translateY(-2px);
}
