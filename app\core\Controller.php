<?php

/**
 * المتحكم الأساسي
 * جميع المتحكمات الأخرى ترث من هذه الفئة
 * يحتوي على الوظائف المشتركة بين جميع المتحكمات
 */
class Controller
{
    protected $db;
    protected $currentUser = null;

    public function __construct()
    {
        // الحصول على مثيل قاعدة البيانات
        $this->db = Database::getInstance();
        
        // تحميل المستخدم الحالي إذا كان مسجل الدخول
        $this->loadCurrentUser();
    }

    /**
     * تحميل ملف العرض (View)
     * @param string $view اسم ملف العرض
     * @param array $data البيانات المرسلة للعرض
     * @param string $layout التخطيط المستخدم
     */
    protected function view($view, $data = [], $layout = 'main')
    {
        // استخراج البيانات لتصبح متغيرات
        extract($data);
        
        // تمرير المستخدم الحالي للعرض
        $currentUser = $this->currentUser;
        
        // تحديد مسار ملف العرض
        $viewFile = "../views/$view.php";
        
        if (file_exists($viewFile)) {
            if ($layout) {
                // استخدام التخطيط
                $content = $this->renderView($viewFile, $data);
                $layoutFile = "../views/layouts/$layout.php";
                
                if (file_exists($layoutFile)) {
                    include $layoutFile;
                } else {
                    // إذا لم يوجد ملف التخطيط، عرض المحتوى مباشرة
                    echo $content;
                }
            } else {
                // عرض بدون تخطيط
                include $viewFile;
            }
        } else {
            throw new Exception("ملف العرض غير موجود: $view");
        }
    }

    /**
     * عرض ملف العرض وإرجاع المحتوى كنص
     */
    private function renderView($viewFile, $data = [])
    {
        extract($data);
        $currentUser = $this->currentUser;
        
        ob_start();
        include $viewFile;
        return ob_get_clean();
    }

    /**
     * إرجاع استجابة JSON
     */
    protected function json($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * إعادة التوجيه
     */
    protected function redirect($path = '', $message = null, $type = 'info')
    {
        if ($message) {
            $this->setFlashMessage($message, $type);
        }
        App::redirect($path);
    }

    /**
     * التحقق من تسجيل الدخول
     */
    protected function requireAuth()
    {
        if (!$this->isLoggedIn()) {
            $this->setFlashMessage('يجب تسجيل الدخول للوصول لهذه الصفحة', 'error');
            $this->redirect('login');
        }
    }

    /**
     * التحقق من نوع المستخدم
     */
    protected function requireUserType($allowedTypes)
    {
        $this->requireAuth();
        
        if (!is_array($allowedTypes)) {
            $allowedTypes = [$allowedTypes];
        }
        
        if (!in_array($this->currentUser['user_type'], $allowedTypes)) {
            $this->setFlashMessage('ليس لديك صلاحية للوصول لهذه الصفحة', 'error');
            $this->redirect('');
        }
    }

    /**
     * التحقق من تسجيل الدخول
     */
    protected function isLoggedIn()
    {
        return $this->currentUser !== null;
    }

    /**
     * تحميل المستخدم الحالي من الجلسة
     */
    private function loadCurrentUser()
    {
        if (isset($_SESSION['user_id'])) {
            $query = "SELECT u.*, d.specialization, d.license_number as doctor_license,
                             p.pharmacy_name, p.license_number as pharmacist_license
                      FROM users u
                      LEFT JOIN doctors d ON u.id = d.user_id
                      LEFT JOIN pharmacists p ON u.id = p.user_id
                      WHERE u.id = :user_id AND u.is_active = 1";
            
            $this->currentUser = $this->db->selectOne($query, [':user_id' => $_SESSION['user_id']]);
        }
    }

    /**
     * تسجيل دخول المستخدم
     */
    protected function loginUser($user)
    {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_type'] = $user['user_type'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        
        // تحديث وقت آخر دخول
        $this->db->update(
            "UPDATE users SET updated_at = NOW() WHERE id = :id",
            [':id' => $user['id']]
        );
        
        $this->currentUser = $user;
    }

    /**
     * تسجيل خروج المستخدم
     */
    protected function logoutUser()
    {
        session_destroy();
        $this->currentUser = null;
    }

    /**
     * تعيين رسالة فلاش
     */
    protected function setFlashMessage($message, $type = 'info')
    {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }

    /**
     * الحصول على رسالة فلاش
     */
    protected function getFlashMessage()
    {
        if (isset($_SESSION['flash_message'])) {
            $message = [
                'text' => $_SESSION['flash_message'],
                'type' => $_SESSION['flash_type'] ?? 'info'
            ];
            
            unset($_SESSION['flash_message']);
            unset($_SESSION['flash_type']);
            
            return $message;
        }
        
        return null;
    }

    /**
     * التحقق من صحة البيانات
     */
    protected function validate($data, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            
            // التحقق من الحقول المطلوبة
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[$field] = $rule['message'] ?? "حقل $field مطلوب";
                continue;
            }
            
            // التحقق من البريد الإلكتروني
            if (isset($rule['email']) && $rule['email'] && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[$field] = $rule['message'] ?? "البريد الإلكتروني غير صحيح";
            }
            
            // التحقق من الحد الأدنى للطول
            if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                $errors[$field] = $rule['message'] ?? "يجب أن يكون $field على الأقل {$rule['min_length']} أحرف";
            }
            
            // التحقق من الحد الأقصى للطول
            if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                $errors[$field] = $rule['message'] ?? "يجب أن يكون $field أقل من {$rule['max_length']} حرف";
            }
            
            // التحقق من تطابق كلمة المرور
            if (isset($rule['match']) && $value !== ($data[$rule['match']] ?? '')) {
                $errors[$field] = $rule['message'] ?? "كلمات المرور غير متطابقة";
            }
        }
        
        return $errors;
    }

    /**
     * تنظيف البيانات المدخلة
     */
    protected function sanitize($data)
    {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    /**
     * إنشاء رمز CSRF
     */
    protected function generateCSRFToken()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * التحقق من رمز CSRF
     */
    protected function verifyCSRFToken($token)
    {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * تسجيل الأنشطة
     */
    protected function logActivity($action, $details = null)
    {
        if ($this->currentUser) {
            $query = "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) 
                      VALUES (:user_id, :action, :details, :ip_address, :user_agent, NOW())";
            
            $this->db->insert($query, [
                ':user_id' => $this->currentUser['id'],
                ':action' => $action,
                ':details' => $details,
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        }
    }

    /**
     * معالجة رفع الملفات
     */
    protected function uploadFile($file, $allowedTypes = null, $maxSize = null)
    {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return ['success' => false, 'message' => 'لم يتم رفع أي ملف'];
        }
        
        $allowedTypes = $allowedTypes ?? ALLOWED_FILE_TYPES;
        $maxSize = $maxSize ?? MAX_FILE_SIZE;
        
        // التحقق من حجم الملف
        if ($file['size'] > $maxSize) {
            return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
        }
        
        // التحقق من نوع الملف
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $allowedTypes)) {
            return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
        }
        
        // إنشاء اسم ملف فريد
        $fileName = uniqid() . '.' . $fileExtension;
        $uploadPath = UPLOAD_PATH . $fileName;
        
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            return ['success' => true, 'filename' => $fileName, 'path' => $uploadPath];
        } else {
            return ['success' => false, 'message' => 'فشل في رفع الملف'];
        }
    }
}
