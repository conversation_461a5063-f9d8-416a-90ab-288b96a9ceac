# HealthKey - نظام إدارة السجلات الطبية الإلكترونية

## نظرة عامة
HealthKey هو نظام شامل لإدارة السجلات الطبية الإلكترونية مصمم لتسهيل التواصل بين المرضى والأطباء والصيادلة. يوفر النظام منصة آمنة وسهلة الاستخدام لإدارة المعلومات الطبية.

## المميزات الرئيسية

### للمرضى
- عرض السجل الطبي الكامل
- إدارة المواعيد
- متابعة الوصفات الطبية
- تحديث الملف الشخصي

### للأطباء
- إدارة المرضى
- إنشاء وصفات طبية
- جدولة المواعيد
- عرض السجلات الطبية

### للصيادلة
- البحث عن الوصفات
- التحقق من صحة الوصفات
- تحديث حالة صرف الأدوية

### للمديرين
- إدارة المستخدمين
- إنشاء التقارير
- مراقبة النظام

## متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مكتبات PHP: PDO, mbstring, json

## التثبيت

1. استنساخ المشروع:
```bash
git clone https://github.com/your-repo/healthkey.git
cd healthkey
```

2. تثبيت التبعيات:
```bash
composer install
```

3. إعداد قاعدة البيانات:
- إنشاء قاعدة بيانات جديدة
- تحديث إعدادات الاتصال في `config.php`
- تشغيل ملفات SQL المطلوبة

4. إعداد الخادم:
- توجيه الخادم إلى مجلد `public`
- التأكد من صلاحيات الكتابة لمجلد `uploads`

## الاستخدام

### تشغيل الخادم المحلي
```bash
composer serve
```

### الوصول للنظام
- الصفحة الرئيسية: `http://localhost:8000`
- تسجيل الدخول: `http://localhost:8000/login`

## هيكل المشروع

```
healthkey/
├── app/                    # منطق التطبيق
│   ├── core/              # الملفات الأساسية
│   ├── controllers/       # المتحكمات
│   ├── models/           # النماذج
│   └── helpers/          # الدوال المساعدة
├── views/                 # واجهات المستخدم
├── public/               # الملفات العامة
└── config.php           # إعدادات التطبيق
```

## الأمان
- تشفير كلمات المرور
- حماية من SQL Injection
- التحقق من صحة المدخلات
- إدارة الجلسات الآمنة

## المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال Pull Request.

## الترخيص
هذا المشروع مرخص تحت رخصة MIT.

## الدعم
للحصول على الدعم، يرجى إنشاء issue في GitHub أو التواصل معنا عبر البريد الإلكتروني.

---

© 2025 HealthKey Team. جميع الحقوق محفوظة.
