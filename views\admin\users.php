<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">إدارة المستخدمين</h1>
        <p class="text-muted">إدارة جميع مستخدمي النظام</p>
    </div>
    <div>
        <a href="<?= App::url('admin/add-user') ?>" class="btn btn-primary">
            <i class="bi bi-person-plus"></i>
            إضافة مستخدم جديد
        </a>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= App::url('admin/users') ?>" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($filters['search']) ?>" 
                       placeholder="الاسم، البريد الإلكتروني، أو الهاتف">
            </div>
            <div class="col-md-2">
                <label for="user_type" class="form-label">نوع المستخدم</label>
                <select class="form-select" id="user_type" name="user_type">
                    <option value="">جميع الأنواع</option>
                    <option value="patient" <?= $filters['user_type'] === 'patient' ? 'selected' : '' ?>>مريض</option>
                    <option value="doctor" <?= $filters['user_type'] === 'doctor' ? 'selected' : '' ?>>طبيب</option>
                    <option value="pharmacist" <?= $filters['user_type'] === 'pharmacist' ? 'selected' : '' ?>>صيدلي</option>
                    <option value="admin" <?= $filters['user_type'] === 'admin' ? 'selected' : '' ?>>مدير</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?= $filters['status'] === 'active' ? 'selected' : '' ?>>نشط</option>
                    <option value="inactive" <?= $filters['status'] === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                    <a href="<?= App::url('admin/users') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary"><?= $totalUsers ?></h4>
                <p class="mb-0">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success"><?= count(array_filter($users, fn($u) => $u['is_active'])) ?></h4>
                <p class="mb-0">المستخدمين النشطين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning"><?= count(array_filter($users, fn($u) => !$u['is_active'])) ?></h4>
                <p class="mb-0">المستخدمين غير النشطين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info"><?= count(array_filter($users, fn($u) => $u['created_at'] >= date('Y-m-01'))) ?></h4>
                <p class="mb-0">جدد هذا الشهر</p>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة المستخدمين</h5>
    </div>
    <div class="card-body">
        <?php if (!empty($users)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>النوع</th>
                            <th>معلومات الاتصال</th>
                            <th>تاريخ التسجيل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-md bg-light rounded-circle me-3 d-flex align-items-center justify-content-center">
                                            <i class="bi bi-person text-muted"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?= User::getFullName($user) ?></h6>
                                            <small class="text-muted">ID: <?= $user['id'] ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?= User::getTypeColor($user['user_type']) ?>">
                                        <?= User::getTypeLabel($user['user_type']) ?>
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <i class="bi bi-envelope me-1"></i>
                                        <small><?= htmlspecialchars($user['email']) ?></small>
                                    </div>
                                    <?php if (!empty($user['phone'])): ?>
                                        <div>
                                            <i class="bi bi-telephone me-1"></i>
                                            <small><?= htmlspecialchars($user['phone']) ?></small>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?= DateHelper::formatArabic($user['created_at']) ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                        <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= App::url('admin/edit-user/' . $user['id']) ?>" 
                                           class="btn btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button class="btn btn-outline-<?= $user['is_active'] ? 'warning' : 'success' ?>" 
                                                onclick="toggleUserStatus(<?= $user['id'] ?>)" 
                                                title="<?= $user['is_active'] ? 'إلغاء التفعيل' : 'تفعيل' ?>">
                                            <i class="bi bi-<?= $user['is_active'] ? 'pause' : 'play' ?>"></i>
                                        </button>
                                        <?php if ($user['id'] != $currentUser['id']): ?>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteUser(<?= $user['id'] ?>)" 
                                                    title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="تصفح الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($pagination['prev']): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $pagination['prev'] ?>">السابق</a>
                            </li>
                        <?php endif; ?>

                        <?php foreach ($pagination['pages'] as $page): ?>
                            <li class="page-item <?= $page['current'] ? 'active' : '' ?>">
                                <a class="page-link" href="<?= $page['url'] ?>"><?= $page['number'] ?></a>
                            </li>
                        <?php endforeach; ?>

                        <?php if ($pagination['next']): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $pagination['next'] ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>

        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-person-x display-1 text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مستخدمين</h4>
                <p class="text-muted">لم يتم العثور على مستخدمين مطابقين لمعايير البحث</p>
                <a href="<?= App::url('admin/add-user') ?>" class="btn btn-primary">
                    <i class="bi bi-person-plus"></i>
                    إضافة مستخدم جديد
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إجراءات جماعية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>اختر الإجراء المطلوب تطبيقه على المستخدمين المحددين:</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="bulkActivate()">
                        <i class="bi bi-check-circle"></i>
                        تفعيل المحددين
                    </button>
                    <button class="btn btn-warning" onclick="bulkDeactivate()">
                        <i class="bi bi-pause-circle"></i>
                        إلغاء تفعيل المحددين
                    </button>
                    <button class="btn btn-danger" onclick="bulkDelete()">
                        <i class="bi bi-trash"></i>
                        حذف المحددين
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleUserStatus(userId) {
    if (confirm('هل تريد تغيير حالة هذا المستخدم؟')) {
        fetch('<?= App::url('admin/toggle-user-status') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'user_id=' + userId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'error');
        });
    }
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch('<?= App::url('admin/delete-user') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'user_id=' + userId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'error');
        });
    }
}

// Bulk selection functionality
let selectedUsers = [];

function toggleUserSelection(userId) {
    const index = selectedUsers.indexOf(userId);
    if (index > -1) {
        selectedUsers.splice(index, 1);
    } else {
        selectedUsers.push(userId);
    }
    updateBulkActionsButton();
}

function selectAllUsers() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    const selectAll = document.getElementById('selectAll').checked;
    
    selectedUsers = [];
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll;
        if (selectAll) {
            selectedUsers.push(parseInt(checkbox.value));
        }
    });
    updateBulkActionsButton();
}

function updateBulkActionsButton() {
    const bulkButton = document.getElementById('bulkActionsButton');
    if (bulkButton) {
        bulkButton.style.display = selectedUsers.length > 0 ? 'block' : 'none';
        bulkButton.textContent = `إجراءات جماعية (${selectedUsers.length})`;
    }
}

function bulkActivate() {
    performBulkAction('activate', 'تفعيل');
}

function bulkDeactivate() {
    performBulkAction('deactivate', 'إلغاء تفعيل');
}

function bulkDelete() {
    if (confirm('هل أنت متأكد من حذف المستخدمين المحددين؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        performBulkAction('delete', 'حذف');
    }
}

function performBulkAction(action, actionName) {
    if (selectedUsers.length === 0) {
        showAlert('يرجى تحديد مستخدمين أولاً', 'warning');
        return;
    }

    fetch('<?= App::url('admin/bulk-user-action') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: action,
            user_ids: selectedUsers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`تم ${actionName} ${data.count} مستخدم بنجاح`, 'success');
            location.reload();
        } else {
            showAlert(data.message, 'error');
        }
    })
    .catch(error => {
        showAlert('حدث خطأ في الاتصال', 'error');
    });

    // إغلاق المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal'));
    modal.hide();
}

// Export functionality
function exportUsers() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = '<?= App::url('admin/users') ?>?' + params.toString();
}
</script>
