<?php

/**
 * فئة التطبيق الرئيسية (Router)
 * مسؤولة عن معالجة مسارات URL وتوجيه الطلبات إلى المتحكم المناسب
 */
class App
{
    protected $controller = 'PagesController';
    protected $method = 'index';
    protected $params = [];
    protected $routes = [];

    public function __construct()
    {
        $this->setupRoutes();
        $this->parseUrl();
    }

    /**
     * إعداد المسارات المخصصة
     */
    private function setupRoutes()
    {
        $this->routes = [
            // المسارات العامة
            '' => ['controller' => 'PagesController', 'method' => 'index'],
            'home' => ['controller' => 'PagesController', 'method' => 'index'],
            'about' => ['controller' => 'PagesController', 'method' => 'about'],
            
            // مسارات المصادقة
            'login' => ['controller' => 'AuthController', 'method' => 'login'],
            'register' => ['controller' => 'AuthController', 'method' => 'register'],
            'logout' => ['controller' => 'AuthController', 'method' => 'logout'],
            
            // مسارات المريض
            'patient' => ['controller' => 'PatientController', 'method' => 'dashboard'],
            'patient/dashboard' => ['controller' => 'PatientController', 'method' => 'dashboard'],
            'patient/profile' => ['controller' => 'PatientController', 'method' => 'profile'],
            'patient/medical-record' => ['controller' => 'PatientController', 'method' => 'medicalRecord'],
            'patient/prescriptions' => ['controller' => 'PatientController', 'method' => 'prescriptions'],
            'patient/appointments' => ['controller' => 'PatientController', 'method' => 'appointments'],
            
            // مسارات الطبيب
            'doctor' => ['controller' => 'DoctorController', 'method' => 'dashboard'],
            'doctor/dashboard' => ['controller' => 'DoctorController', 'method' => 'dashboard'],
            'doctor/patients' => ['controller' => 'DoctorController', 'method' => 'patients'],
            'doctor/appointments' => ['controller' => 'DoctorController', 'method' => 'appointments'],
            'doctor/prescriptions' => ['controller' => 'DoctorController', 'method' => 'prescriptions'],
            
            // مسارات الصيدلي
            'pharmacist' => ['controller' => 'PharmacistController', 'method' => 'dashboard'],
            'pharmacist/dashboard' => ['controller' => 'PharmacistController', 'method' => 'dashboard'],
            'pharmacist/prescriptions' => ['controller' => 'PharmacistController', 'method' => 'prescriptions'],
            
            // مسارات المدير
            'admin' => ['controller' => 'AdminController', 'method' => 'dashboard'],
            'admin/dashboard' => ['controller' => 'AdminController', 'method' => 'dashboard'],
            'admin/users' => ['controller' => 'AdminController', 'method' => 'manageUsers'],
            'admin/reports' => ['controller' => 'AdminController', 'method' => 'reports'],
        ];
    }

    /**
     * تحليل URL وتحديد المتحكم والطريقة والمعاملات
     */
    private function parseUrl()
    {
        if (isset($_GET['url'])) {
            $url = rtrim($_GET['url'], '/');
            $url = filter_var($url, FILTER_SANITIZE_URL);
            $url = explode('/', $url);
        } else {
            $url = [''];
        }

        $path = implode('/', $url);

        // البحث في المسارات المخصصة أولاً
        if (isset($this->routes[$path])) {
            $this->controller = $this->routes[$path]['controller'];
            $this->method = $this->routes[$path]['method'];
            $this->params = array_slice($url, 2); // المعاملات الإضافية
        } else {
            // التحليل التقليدي للمسار
            $this->parseTraditionalUrl($url);
        }

        $this->loadController();
    }

    /**
     * تحليل المسار بالطريقة التقليدية (controller/method/params)
     */
    private function parseTraditionalUrl($url)
    {
        // تحديد المتحكم
        if (isset($url[0]) && !empty($url[0])) {
            $controllerName = ucfirst($url[0]) . 'Controller';
            if (file_exists('../app/controllers/' . $controllerName . '.php')) {
                $this->controller = $controllerName;
                unset($url[0]);
            }
        }

        // تحديد الطريقة
        if (isset($url[1]) && !empty($url[1])) {
            $this->method = $url[1];
            unset($url[1]);
        }

        // تحديد المعاملات
        $this->params = $url ? array_values($url) : [];
    }

    /**
     * تحميل المتحكم وتنفيذ الطريقة
     */
    private function loadController()
    {
        // تحميل ملف المتحكم
        $controllerFile = '../app/controllers/' . $this->controller . '.php';
        
        if (file_exists($controllerFile)) {
            require_once $controllerFile;
            
            // إنشاء مثيل من المتحكم
            $this->controller = new $this->controller;
            
            // التحقق من وجود الطريقة
            if (method_exists($this->controller, $this->method)) {
                call_user_func_array([$this->controller, $this->method], $this->params);
            } else {
                $this->show404();
            }
        } else {
            $this->show404();
        }
    }

    /**
     * عرض صفحة 404
     */
    private function show404()
    {
        http_response_code(404);
        require_once '../views/errors/404.php';
        exit;
    }

    /**
     * إضافة مسار مخصص
     */
    public static function addRoute($path, $controller, $method)
    {
        $this->routes[$path] = [
            'controller' => $controller,
            'method' => $method
        ];
    }

    /**
     * الحصول على URL الأساسي
     */
    public static function getBaseUrl()
    {
        return APP_URL;
    }

    /**
     * إنشاء رابط
     */
    public static function url($path = '')
    {
        $baseUrl = rtrim(APP_URL, '/');
        $path = ltrim($path, '/');
        return $baseUrl . '/' . $path;
    }

    /**
     * إعادة التوجيه
     */
    public static function redirect($path = '')
    {
        $url = self::url($path);
        header("Location: $url");
        exit;
    }

    /**
     * التحقق من نوع الطلب
     */
    public static function isPost()
    {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }

    public static function isGet()
    {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }

    public static function isAjax()
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * الحصول على بيانات POST بشكل آمن
     */
    public static function post($key = null, $default = null)
    {
        if ($key === null) {
            return $_POST;
        }
        return isset($_POST[$key]) ? $_POST[$key] : $default;
    }

    /**
     * الحصول على بيانات GET بشكل آمن
     */
    public static function get($key = null, $default = null)
    {
        if ($key === null) {
            return $_GET;
        }
        return isset($_GET[$key]) ? $_GET[$key] : $default;
    }
}
