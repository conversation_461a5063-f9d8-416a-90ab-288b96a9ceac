<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">مرحباً، <?= htmlspecialchars($patient['first_name']) ?></h1>
        <p class="text-muted">إليك ملخص عن حالتك الصحية</p>
    </div>
    <div>
        <?php if ($unreadNotifications > 0): ?>
            <a href="<?= App::url('patient/notifications') ?>" class="btn btn-outline-primary position-relative">
                <i class="bi bi-bell"></i>
                الإشعارات
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    <?= $unreadNotifications ?>
                </span>
            </a>
        <?php endif; ?>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($upcomingAppointments) ?></h4>
                        <p class="mb-0">المواعيد القادمة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($recentPrescriptions) ?></h4>
                        <p class="mb-0">الوصفات الحديثة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $medicalSummary['total_visits'] ?></h4>
                        <p class="mb-0">إجمالي الزيارات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard2-pulse display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($medicalSummary['allergies']) ?></h4>
                        <p class="mb-0">الحساسيات المسجلة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- المواعيد القادمة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-check me-2"></i>
                    المواعيد القادمة
                </h5>
                <a href="<?= App::url('patient/appointments') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($upcomingAppointments)): ?>
                    <?php foreach ($upcomingAppointments as $appointment): ?>
                        <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">د. <?= htmlspecialchars($appointment['other_user_name']) ?></h6>
                                <p class="mb-1 text-muted">
                                    <i class="bi bi-calendar me-1"></i>
                                    <?= DateHelper::formatArabic($appointment['appointment_date']) ?>
                                </p>
                                <p class="mb-0 text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    <?= DateHelper::formatTime($appointment['appointment_time']) ?>
                                </p>
                            </div>
                            <div>
                                <span class="badge bg-<?= Appointment::getStatusColor($appointment['status']) ?>">
                                    <?= Appointment::getStatusLabel($appointment['status']) ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-calendar-x display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مواعيد قادمة</p>
                        <a href="<?= App::url('patient/book-appointment') ?>" class="btn btn-primary">
                            احجز موعد جديد
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- الوصفات الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-prescription2 me-2"></i>
                    الوصفات الحديثة
                </h5>
                <a href="<?= App::url('patient/prescriptions') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentPrescriptions)): ?>
                    <?php foreach ($recentPrescriptions as $prescription): ?>
                        <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="<?= App::url('patient/prescription/' . $prescription['id']) ?>" class="text-decoration-none">
                                        <?= htmlspecialchars($prescription['prescription_code']) ?>
                                    </a>
                                </h6>
                                <p class="mb-1 text-muted">د. <?= htmlspecialchars($prescription['doctor_name']) ?></p>
                                <p class="mb-0 text-muted">
                                    <i class="bi bi-calendar me-1"></i>
                                    <?= DateHelper::formatArabic($prescription['issue_date']) ?>
                                </p>
                            </div>
                            <div>
                                <span class="badge bg-<?= $prescription['status'] === 'active' ? 'success' : 'secondary' ?>">
                                    <?= $prescription['status'] === 'active' ? 'نشطة' : 'منتهية' ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-prescription display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد وصفات طبية</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الإشعارات الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-bell me-2"></i>
                    الإشعارات الحديثة
                </h5>
                <a href="<?= App::url('patient/notifications') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($notifications)): ?>
                    <?php foreach ($notifications as $notification): ?>
                        <div class="d-flex align-items-start mb-3 p-3 bg-light rounded <?= !$notification['is_read'] ? 'border-start border-primary border-3' : '' ?>">
                            <div class="me-3">
                                <i class="bi <?= Notification::getTypeIcon($notification['type']) ?> text-<?= Notification::getTypeColor($notification['type']) ?>"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?= htmlspecialchars($notification['title']) ?></h6>
                                <p class="mb-1 text-muted small"><?= htmlspecialchars($notification['message']) ?></p>
                                <p class="mb-0 text-muted small">
                                    <?= Notification::formatTime($notification['created_at']) ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-bell-slash display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد إشعارات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- ملخص السجل الطبي -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-clipboard2-pulse me-2"></i>
                    ملخص السجل الطبي
                </h5>
                <a href="<?= App::url('patient/medical-record') ?>" class="btn btn-sm btn-outline-primary">
                    عرض التفاصيل
                </a>
            </div>
            <div class="card-body">
                <!-- آخر زيارة -->
                <?php if (!empty($medicalSummary['last_visit'])): ?>
                    <div class="mb-3">
                        <h6 class="text-primary">آخر زيارة</h6>
                        <p class="mb-1">د. <?= htmlspecialchars($medicalSummary['last_visit']['doctor_name']) ?></p>
                        <p class="mb-1 text-muted">
                            <?= DateHelper::formatArabic($medicalSummary['last_visit']['visit_date']) ?>
                        </p>
                        <?php if (!empty($medicalSummary['last_visit']['diagnosis'])): ?>
                            <p class="mb-0 text-muted small">
                                التشخيص: <?= htmlspecialchars($medicalSummary['last_visit']['diagnosis']) ?>
                            </p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- الحساسيات -->
                <?php if (!empty($medicalSummary['allergies'])): ?>
                    <div class="mb-3">
                        <h6 class="text-warning">الحساسيات</h6>
                        <?php foreach (array_slice($medicalSummary['allergies'], 0, 3) as $allergy): ?>
                            <span class="badge bg-warning text-dark me-1 mb-1">
                                <?= htmlspecialchars($allergy['allergen']) ?>
                            </span>
                        <?php endforeach; ?>
                        <?php if (count($medicalSummary['allergies']) > 3): ?>
                            <span class="text-muted small">
                                و <?= count($medicalSummary['allergies']) - 3 ?> أخرى
                            </span>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- الفحوصات الحديثة -->
                <?php if (!empty($medicalSummary['recent_lab_tests'])): ?>
                    <div>
                        <h6 class="text-info">آخر الفحوصات</h6>
                        <?php foreach (array_slice($medicalSummary['recent_lab_tests'], 0, 2) as $test): ?>
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="small"><?= htmlspecialchars($test['test_name']) ?></span>
                                <span class="badge bg-<?= $test['status'] === 'completed' ? 'success' : 'warning' ?> small">
                                    <?= MedicalRecord::getLabTestStatusLabel($test['status']) ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php if (empty($medicalSummary['last_visit']) && empty($medicalSummary['allergies']) && empty($medicalSummary['recent_lab_tests'])): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard2-x display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد سجلات طبية بعد</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('patient/book-appointment') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-calendar-plus display-6 mb-2"></i>
                            <span>حجز موعد</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('patient/prescriptions') ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-prescription2 display-6 mb-2"></i>
                            <span>وصفاتي</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('patient/medical-record') ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-clipboard2-pulse display-6 mb-2"></i>
                            <span>سجلي الطبي</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('patient/profile') ?>" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-person-gear display-6 mb-2"></i>
                            <span>الملف الشخصي</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
