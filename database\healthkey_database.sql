-- HealthKey Database Schema
-- نظام إدارة الرعاية الصحية الإلكترونية
-- تاريخ الإنشاء: 2024

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `healthkey` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `healthkey`;

-- --------------------------------------------------------

-- جدول المستخدمين الرئيسي
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) NOT NULL COMMENT 'الاسم الأول',
  `last_name` varchar(50) NOT NULL COMMENT 'الاسم الأخير',
  `email` varchar(100) NOT NULL UNIQUE COMMENT 'البريد الإلكتروني',
  `phone` varchar(20) DEFAULT NULL COMMENT 'رقم الهاتف',
  `password` varchar(255) NOT NULL COMMENT 'كلمة المرور المشفرة',
  `user_type` enum('admin','doctor','patient','pharmacist') NOT NULL COMMENT 'نوع المستخدم',
  `national_id` varchar(20) DEFAULT NULL UNIQUE COMMENT 'رقم الهوية الوطنية',
  `date_of_birth` date DEFAULT NULL COMMENT 'تاريخ الميلاد',
  `gender` enum('male','female') DEFAULT NULL COMMENT 'الجنس',
  `address` text DEFAULT NULL COMMENT 'العنوان',
  `emergency_contact` varchar(100) DEFAULT NULL COMMENT 'جهة الاتصال في الطوارئ',
  `emergency_phone` varchar(20) DEFAULT NULL COMMENT 'هاتف الطوارئ',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'حالة النشاط',
  `email_verified_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ تأكيد البريد الإلكتروني',
  `remember_token` varchar(100) DEFAULT NULL COMMENT 'رمز التذكر',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT 'آخر تسجيل دخول',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_national_id` (`national_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المستخدمين الرئيسي';

-- --------------------------------------------------------

-- جدول الأطباء
CREATE TABLE `doctors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'معرف المستخدم',
  `specialization` varchar(100) DEFAULT NULL COMMENT 'التخصص',
  `license_number` varchar(50) DEFAULT NULL UNIQUE COMMENT 'رقم الترخيص',
  `years_of_experience` int(11) DEFAULT NULL COMMENT 'سنوات الخبرة',
  `education` text DEFAULT NULL COMMENT 'المؤهلات التعليمية',
  `certifications` text DEFAULT NULL COMMENT 'الشهادات',
  `hospital_affiliation` varchar(100) DEFAULT NULL COMMENT 'الانتماء للمستشفى',
  `consultation_fee` decimal(10,2) DEFAULT NULL COMMENT 'رسوم الاستشارة',
  `working_hours` json DEFAULT NULL COMMENT 'ساعات العمل',
  `bio` text DEFAULT NULL COMMENT 'نبذة شخصية',
  `is_available` tinyint(1) DEFAULT 1 COMMENT 'متاح للمواعيد',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `idx_specialization` (`specialization`),
  KEY `idx_license_number` (`license_number`),
  KEY `idx_is_available` (`is_available`),
  CONSTRAINT `fk_doctors_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الأطباء';

-- --------------------------------------------------------

-- جدول الصيادلة
CREATE TABLE `pharmacists` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'معرف المستخدم',
  `license_number` varchar(50) DEFAULT NULL UNIQUE COMMENT 'رقم ترخيص الصيدلة',
  `pharmacy_name` varchar(100) DEFAULT NULL COMMENT 'اسم الصيدلية',
  `pharmacy_address` text DEFAULT NULL COMMENT 'عنوان الصيدلية',
  `pharmacy_phone` varchar(20) DEFAULT NULL COMMENT 'هاتف الصيدلية',
  `working_hours` json DEFAULT NULL COMMENT 'ساعات العمل',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'حالة النشاط',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `idx_license_number` (`license_number`),
  KEY `idx_pharmacy_name` (`pharmacy_name`),
  CONSTRAINT `fk_pharmacists_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الصيادلة';

-- --------------------------------------------------------

-- جدول المواعيد
CREATE TABLE `appointments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` int(11) NOT NULL COMMENT 'معرف المريض',
  `doctor_id` int(11) NOT NULL COMMENT 'معرف الطبيب',
  `appointment_date` date NOT NULL COMMENT 'تاريخ الموعد',
  `appointment_time` time NOT NULL COMMENT 'وقت الموعد',
  `duration` int(11) DEFAULT 30 COMMENT 'مدة الموعد بالدقائق',
  `reason` text DEFAULT NULL COMMENT 'سبب الزيارة',
  `status` enum('scheduled','confirmed','in_progress','completed','cancelled','no_show') DEFAULT 'scheduled' COMMENT 'حالة الموعد',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `cancellation_reason` text DEFAULT NULL COMMENT 'سبب الإلغاء',
  `reminder_sent` tinyint(1) DEFAULT 0 COMMENT 'تم إرسال التذكير',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_appointment_date` (`appointment_date`),
  KEY `idx_status` (`status`),
  KEY `idx_appointment_datetime` (`appointment_date`, `appointment_time`),
  CONSTRAINT `fk_appointments_patient_id` FOREIGN KEY (`patient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_appointments_doctor_id` FOREIGN KEY (`doctor_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المواعيد';

-- --------------------------------------------------------

-- جدول السجلات الطبية
CREATE TABLE `medical_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` int(11) NOT NULL COMMENT 'معرف المريض',
  `doctor_id` int(11) NOT NULL COMMENT 'معرف الطبيب',
  `appointment_id` int(11) DEFAULT NULL COMMENT 'معرف الموعد',
  `visit_date` date NOT NULL COMMENT 'تاريخ الزيارة',
  `chief_complaint` text DEFAULT NULL COMMENT 'الشكوى الرئيسية',
  `history_of_present_illness` text DEFAULT NULL COMMENT 'تاريخ المرض الحالي',
  `past_medical_history` text DEFAULT NULL COMMENT 'التاريخ الطبي السابق',
  `family_history` text DEFAULT NULL COMMENT 'التاريخ العائلي',
  `social_history` text DEFAULT NULL COMMENT 'التاريخ الاجتماعي',
  `physical_examination` text DEFAULT NULL COMMENT 'الفحص السريري',
  `vital_signs` json DEFAULT NULL COMMENT 'العلامات الحيوية',
  `diagnosis` text DEFAULT NULL COMMENT 'التشخيص',
  `differential_diagnosis` text DEFAULT NULL COMMENT 'التشخيص التفريقي',
  `treatment_plan` text DEFAULT NULL COMMENT 'خطة العلاج',
  `medications` text DEFAULT NULL COMMENT 'الأدوية',
  `follow_up` text DEFAULT NULL COMMENT 'المتابعة',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات إضافية',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_appointment_id` (`appointment_id`),
  KEY `idx_visit_date` (`visit_date`),
  CONSTRAINT `fk_medical_records_patient_id` FOREIGN KEY (`patient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_medical_records_doctor_id` FOREIGN KEY (`doctor_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_medical_records_appointment_id` FOREIGN KEY (`appointment_id`) REFERENCES `appointments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول السجلات الطبية';

-- --------------------------------------------------------

-- جدول الوصفات الطبية
CREATE TABLE `prescriptions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prescription_code` varchar(20) NOT NULL UNIQUE COMMENT 'كود الوصفة',
  `patient_id` int(11) NOT NULL COMMENT 'معرف المريض',
  `doctor_id` int(11) NOT NULL COMMENT 'معرف الطبيب',
  `medical_record_id` int(11) DEFAULT NULL COMMENT 'معرف السجل الطبي',
  `issue_date` date NOT NULL COMMENT 'تاريخ الإصدار',
  `expiry_date` date DEFAULT NULL COMMENT 'تاريخ انتهاء الصلاحية',
  `diagnosis` text DEFAULT NULL COMMENT 'التشخيص',
  `status` enum('active','expired','cancelled','completed') DEFAULT 'active' COMMENT 'حالة الوصفة',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `prescription_code` (`prescription_code`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_medical_record_id` (`medical_record_id`),
  KEY `idx_issue_date` (`issue_date`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_prescriptions_patient_id` FOREIGN KEY (`patient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_prescriptions_doctor_id` FOREIGN KEY (`doctor_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_prescriptions_medical_record_id` FOREIGN KEY (`medical_record_id`) REFERENCES `medical_records` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الوصفات الطبية';

-- --------------------------------------------------------

-- جدول أدوية الوصفات
CREATE TABLE `prescription_medications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prescription_id` int(11) NOT NULL COMMENT 'معرف الوصفة',
  `medication_name` varchar(100) NOT NULL COMMENT 'اسم الدواء',
  `generic_name` varchar(100) DEFAULT NULL COMMENT 'الاسم العلمي',
  `strength` varchar(50) DEFAULT NULL COMMENT 'التركيز',
  `dosage_form` varchar(50) DEFAULT NULL COMMENT 'شكل الجرعة',
  `quantity` int(11) NOT NULL COMMENT 'الكمية',
  `dosage` varchar(100) NOT NULL COMMENT 'الجرعة',
  `frequency` varchar(100) NOT NULL COMMENT 'التكرار',
  `duration` varchar(100) DEFAULT NULL COMMENT 'المدة',
  `instructions` text DEFAULT NULL COMMENT 'تعليمات الاستخدام',
  `is_dispensed` tinyint(1) DEFAULT 0 COMMENT 'تم الصرف',
  `dispensed_quantity` int(11) DEFAULT NULL COMMENT 'الكمية المصروفة',
  `dispensed_by` int(11) DEFAULT NULL COMMENT 'صرف بواسطة',
  `dispensed_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الصرف',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_prescription_id` (`prescription_id`),
  KEY `idx_medication_name` (`medication_name`),
  KEY `idx_is_dispensed` (`is_dispensed`),
  KEY `idx_dispensed_by` (`dispensed_by`),
  CONSTRAINT `fk_prescription_medications_prescription_id` FOREIGN KEY (`prescription_id`) REFERENCES `prescriptions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_prescription_medications_dispensed_by` FOREIGN KEY (`dispensed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول أدوية الوصفات';

-- --------------------------------------------------------

-- جدول الحساسيات
CREATE TABLE `allergies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` int(11) NOT NULL COMMENT 'معرف المريض',
  `allergen` varchar(100) NOT NULL COMMENT 'المادة المسببة للحساسية',
  `reaction` text DEFAULT NULL COMMENT 'رد الفعل',
  `severity` enum('mild','moderate','severe','life_threatening') DEFAULT 'mild' COMMENT 'شدة الحساسية',
  `onset_date` date DEFAULT NULL COMMENT 'تاريخ بداية الحساسية',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'حالة النشاط',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_allergen` (`allergen`),
  KEY `idx_severity` (`severity`),
  CONSTRAINT `fk_allergies_patient_id` FOREIGN KEY (`patient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الحساسيات';

-- --------------------------------------------------------

-- جدول الفحوصات المخبرية
CREATE TABLE `lab_tests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` int(11) NOT NULL COMMENT 'معرف المريض',
  `doctor_id` int(11) NOT NULL COMMENT 'معرف الطبيب المطلوب',
  `medical_record_id` int(11) DEFAULT NULL COMMENT 'معرف السجل الطبي',
  `test_name` varchar(100) NOT NULL COMMENT 'اسم الفحص',
  `test_code` varchar(20) DEFAULT NULL COMMENT 'كود الفحص',
  `test_date` date NOT NULL COMMENT 'تاريخ الفحص',
  `sample_type` varchar(50) DEFAULT NULL COMMENT 'نوع العينة',
  `status` enum('ordered','collected','in_progress','completed','cancelled') DEFAULT 'ordered' COMMENT 'حالة الفحص',
  `results` text DEFAULT NULL COMMENT 'النتائج',
  `reference_range` varchar(100) DEFAULT NULL COMMENT 'المدى المرجعي',
  `interpretation` text DEFAULT NULL COMMENT 'التفسير',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `lab_technician` varchar(100) DEFAULT NULL COMMENT 'فني المختبر',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الإكمال',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_medical_record_id` (`medical_record_id`),
  KEY `idx_test_date` (`test_date`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_lab_tests_patient_id` FOREIGN KEY (`patient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_lab_tests_doctor_id` FOREIGN KEY (`doctor_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_lab_tests_medical_record_id` FOREIGN KEY (`medical_record_id`) REFERENCES `medical_records` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الفحوصات المخبرية';

-- --------------------------------------------------------

-- جدول الإشعارات
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'معرف المستخدم',
  `title` varchar(200) NOT NULL COMMENT 'عنوان الإشعار',
  `message` text NOT NULL COMMENT 'نص الإشعار',
  `type` enum('appointment','prescription','medical_record','system','reminder','general') DEFAULT 'general' COMMENT 'نوع الإشعار',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal' COMMENT 'أولوية الإشعار',
  `is_read` tinyint(1) DEFAULT 0 COMMENT 'تم القراءة',
  `read_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ القراءة',
  `data` json DEFAULT NULL COMMENT 'بيانات إضافية',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء الصلاحية',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_priority` (`priority`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_notifications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الإشعارات';

-- --------------------------------------------------------

-- جدول الملفات المرفقة
CREATE TABLE `attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attachable_type` varchar(50) NOT NULL COMMENT 'نوع الكائن المرتبط',
  `attachable_id` int(11) NOT NULL COMMENT 'معرف الكائن المرتبط',
  `file_name` varchar(255) NOT NULL COMMENT 'اسم الملف',
  `original_name` varchar(255) NOT NULL COMMENT 'الاسم الأصلي',
  `file_path` varchar(500) NOT NULL COMMENT 'مسار الملف',
  `file_size` bigint(20) NOT NULL COMMENT 'حجم الملف',
  `file_type` varchar(100) NOT NULL COMMENT 'نوع الملف',
  `mime_type` varchar(100) NOT NULL COMMENT 'نوع MIME',
  `description` text DEFAULT NULL COMMENT 'وصف الملف',
  `uploaded_by` int(11) NOT NULL COMMENT 'رفع بواسطة',
  `is_public` tinyint(1) DEFAULT 0 COMMENT 'ملف عام',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_attachable` (`attachable_type`, `attachable_id`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_file_type` (`file_type`),
  CONSTRAINT `fk_attachments_uploaded_by` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الملفات المرفقة';

-- --------------------------------------------------------

-- جدول سجل النشاطات
CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT 'معرف المستخدم',
  `action` varchar(100) NOT NULL COMMENT 'الإجراء',
  `description` text NOT NULL COMMENT 'وصف النشاط',
  `subject_type` varchar(50) DEFAULT NULL COMMENT 'نوع الكائن',
  `subject_id` int(11) DEFAULT NULL COMMENT 'معرف الكائن',
  `properties` json DEFAULT NULL COMMENT 'خصائص إضافية',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'عنوان IP',
  `user_agent` text DEFAULT NULL COMMENT 'معلومات المتصفح',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_subject` (`subject_type`, `subject_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_activity_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول سجل النشاطات';

-- --------------------------------------------------------

-- جدول إعدادات النظام
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL UNIQUE COMMENT 'مفتاح الإعداد',
  `value` text DEFAULT NULL COMMENT 'قيمة الإعداد',
  `type` enum('string','integer','boolean','json','text') DEFAULT 'string' COMMENT 'نوع البيانات',
  `description` text DEFAULT NULL COMMENT 'وصف الإعداد',
  `is_public` tinyint(1) DEFAULT 0 COMMENT 'إعداد عام',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول إعدادات النظام';

-- --------------------------------------------------------

-- جدول جلسات المستخدمين
CREATE TABLE `user_sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_activity` (`last_activity`),
  CONSTRAINT `fk_user_sessions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول جلسات المستخدمين';

-- --------------------------------------------------------

-- إدراج البيانات الأولية

-- إدراج المدير الافتراضي
INSERT INTO `users` (`first_name`, `last_name`, `email`, `password`, `user_type`, `is_active`, `email_verified_at`) VALUES
('مدير', 'النظام', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, NOW());

-- إدراج طبيب تجريبي
INSERT INTO `users` (`first_name`, `last_name`, `email`, `phone`, `password`, `user_type`, `national_id`, `date_of_birth`, `gender`, `is_active`, `email_verified_at`) VALUES
('أحمد', 'محمد', '<EMAIL>', '**********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'doctor', '**********', '1980-01-01', 'male', 1, NOW());

-- إدراج مريض تجريبي
INSERT INTO `users` (`first_name`, `last_name`, `email`, `phone`, `password`, `user_type`, `national_id`, `date_of_birth`, `gender`, `is_active`, `email_verified_at`) VALUES
('فاطمة', 'علي', '<EMAIL>', '**********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'patient', '**********', '1990-05-15', 'female', 1, NOW());

-- إدراج صيدلي تجريبي
INSERT INTO `users` (`first_name`, `last_name`, `email`, `phone`, `password`, `user_type`, `national_id`, `date_of_birth`, `gender`, `is_active`, `email_verified_at`) VALUES
('خالد', 'السعد', '<EMAIL>', '**********', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'pharmacist', '**********', '1985-03-20', 'male', 1, NOW());

-- إدراج بيانات الطبيب
INSERT INTO `doctors` (`user_id`, `specialization`, `license_number`, `years_of_experience`, `education`, `hospital_affiliation`, `consultation_fee`, `is_available`) VALUES
(2, 'طب الأسرة', 'DOC001', 10, 'بكالوريوس الطب والجراحة - جامعة الملك سعود', 'مستشفى الملك فهد', 200.00, 1);

-- إدراج بيانات الصيدلي
INSERT INTO `pharmacists` (`user_id`, `license_number`, `pharmacy_name`, `pharmacy_address`, `pharmacy_phone`, `is_active`) VALUES
(4, 'PHARM001', 'صيدلية النهضة', 'شارع الملك فهد، الرياض', '**********', 1);

-- إدراج إعدادات النظام الافتراضية
INSERT INTO `system_settings` (`key`, `value`, `type`, `description`, `is_public`) VALUES
('app_name', 'HealthKey', 'string', 'اسم التطبيق', 1),
('app_version', '1.0.0', 'string', 'إصدار التطبيق', 1),
('timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية', 0),
('date_format', 'Y-m-d', 'string', 'تنسيق التاريخ', 0),
('time_format', 'H:i', 'string', 'تنسيق الوقت', 0),
('max_file_size', '10485760', 'integer', 'الحد الأقصى لحجم الملف (بايت)', 0),
('allowed_file_types', '["jpg","jpeg","png","pdf","doc","docx"]', 'json', 'أنواع الملفات المسموحة', 0),
('appointment_duration', '30', 'integer', 'مدة الموعد الافتراضية (دقيقة)', 0),
('prescription_validity_days', '30', 'integer', 'مدة صلاحية الوصفة (يوم)', 0),
('enable_notifications', 'true', 'boolean', 'تفعيل الإشعارات', 0),
('enable_email_notifications', 'true', 'boolean', 'تفعيل إشعارات البريد الإلكتروني', 0),
('enable_sms_notifications', 'false', 'boolean', 'تفعيل إشعارات الرسائل النصية', 0);

-- --------------------------------------------------------

-- إنشاء الفهارس الإضافية لتحسين الأداء

-- فهارس مركبة للبحث السريع
CREATE INDEX `idx_users_type_active` ON `users` (`user_type`, `is_active`);
CREATE INDEX `idx_appointments_doctor_date` ON `appointments` (`doctor_id`, `appointment_date`);
CREATE INDEX `idx_appointments_patient_date` ON `appointments` (`patient_id`, `appointment_date`);
CREATE INDEX `idx_prescriptions_patient_status` ON `prescriptions` (`patient_id`, `status`);
CREATE INDEX `idx_prescriptions_doctor_date` ON `prescriptions` (`doctor_id`, `issue_date`);
CREATE INDEX `idx_medical_records_patient_date` ON `medical_records` (`patient_id`, `visit_date`);
CREATE INDEX `idx_notifications_user_read` ON `notifications` (`user_id`, `is_read`);

-- فهارس النص الكامل للبحث
ALTER TABLE `users` ADD FULLTEXT(`first_name`, `last_name`, `email`);
ALTER TABLE `medical_records` ADD FULLTEXT(`chief_complaint`, `diagnosis`, `notes`);
ALTER TABLE `prescriptions` ADD FULLTEXT(`diagnosis`, `notes`);

-- --------------------------------------------------------

-- إنشاء المشاهدات (Views) لتسهيل الاستعلامات

-- مشاهدة المواعيد مع تفاصيل المريض والطبيب
CREATE VIEW `appointments_view` AS
SELECT
    a.id,
    a.appointment_date,
    a.appointment_time,
    a.duration,
    a.reason,
    a.status,
    a.notes,
    CONCAT(p.first_name, ' ', p.last_name) AS patient_name,
    p.phone AS patient_phone,
    p.email AS patient_email,
    CONCAT(d.first_name, ' ', d.last_name) AS doctor_name,
    doc.specialization AS doctor_specialization,
    a.created_at,
    a.updated_at
FROM `appointments` a
JOIN `users` p ON a.patient_id = p.id
JOIN `users` d ON a.doctor_id = d.id
LEFT JOIN `doctors` doc ON d.id = doc.user_id;

-- مشاهدة الوصفات مع تفاصيل المريض والطبيب
CREATE VIEW `prescriptions_view` AS
SELECT
    pr.id,
    pr.prescription_code,
    pr.issue_date,
    pr.expiry_date,
    pr.diagnosis,
    pr.status,
    pr.notes,
    CONCAT(p.first_name, ' ', p.last_name) AS patient_name,
    p.phone AS patient_phone,
    CONCAT(d.first_name, ' ', d.last_name) AS doctor_name,
    doc.specialization AS doctor_specialization,
    pr.created_at,
    pr.updated_at
FROM `prescriptions` pr
JOIN `users` p ON pr.patient_id = p.id
JOIN `users` d ON pr.doctor_id = d.id
LEFT JOIN `doctors` doc ON d.id = doc.user_id;

-- --------------------------------------------------------

-- إنشاء المحفزات (Triggers) للتحديث التلقائي

DELIMITER $$

-- محفز لتحديث كود الوصفة تلقائياً
CREATE TRIGGER `generate_prescription_code`
BEFORE INSERT ON `prescriptions`
FOR EACH ROW
BEGIN
    IF NEW.prescription_code IS NULL OR NEW.prescription_code = '' THEN
        SET NEW.prescription_code = CONCAT('RX', YEAR(NOW()), LPAD(MONTH(NOW()), 2, '0'), LPAD(DAY(NOW()), 2, '0'), LPAD((SELECT COALESCE(MAX(id), 0) + 1 FROM prescriptions), 4, '0'));
    END IF;
END$$

-- محفز لتسجيل النشاط عند إنشاء موعد جديد
CREATE TRIGGER `log_appointment_created`
AFTER INSERT ON `appointments`
FOR EACH ROW
BEGIN
    INSERT INTO `activity_logs` (`user_id`, `action`, `description`, `subject_type`, `subject_id`, `created_at`)
    VALUES (NEW.patient_id, 'appointment_created', CONCAT('تم حجز موعد جديد في ', NEW.appointment_date, ' الساعة ', NEW.appointment_time), 'appointment', NEW.id, NOW());
END$$

-- محفز لتسجيل النشاط عند تحديث حالة الموعد
CREATE TRIGGER `log_appointment_status_updated`
AFTER UPDATE ON `appointments`
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO `activity_logs` (`user_id`, `action`, `description`, `subject_type`, `subject_id`, `created_at`)
        VALUES (NEW.doctor_id, 'appointment_status_updated', CONCAT('تم تحديث حالة الموعد من ', OLD.status, ' إلى ', NEW.status), 'appointment', NEW.id, NOW());
    END IF;
END$$

DELIMITER ;

-- --------------------------------------------------------

-- إنشاء الإجراءات المخزنة (Stored Procedures)

DELIMITER $$

-- إجراء للحصول على إحصائيات المريض
CREATE PROCEDURE `GetPatientStats`(IN patient_id INT)
BEGIN
    SELECT
        (SELECT COUNT(*) FROM appointments WHERE patient_id = patient_id) AS total_appointments,
        (SELECT COUNT(*) FROM appointments WHERE patient_id = patient_id AND status = 'completed') AS completed_appointments,
        (SELECT COUNT(*) FROM prescriptions WHERE patient_id = patient_id) AS total_prescriptions,
        (SELECT COUNT(*) FROM prescriptions WHERE patient_id = patient_id AND status = 'active') AS active_prescriptions,
        (SELECT COUNT(*) FROM medical_records WHERE patient_id = patient_id) AS total_medical_records,
        (SELECT COUNT(*) FROM allergies WHERE patient_id = patient_id AND is_active = 1) AS active_allergies;
END$$

-- إجراء للحصول على إحصائيات الطبيب
CREATE PROCEDURE `GetDoctorStats`(IN doctor_id INT)
BEGIN
    SELECT
        (SELECT COUNT(*) FROM appointments WHERE doctor_id = doctor_id) AS total_appointments,
        (SELECT COUNT(*) FROM appointments WHERE doctor_id = doctor_id AND status = 'completed') AS completed_appointments,
        (SELECT COUNT(*) FROM appointments WHERE doctor_id = doctor_id AND appointment_date = CURDATE()) AS today_appointments,
        (SELECT COUNT(*) FROM prescriptions WHERE doctor_id = doctor_id) AS total_prescriptions,
        (SELECT COUNT(*) FROM medical_records WHERE doctor_id = doctor_id) AS total_medical_records,
        (SELECT COUNT(DISTINCT patient_id) FROM appointments WHERE doctor_id = doctor_id) AS unique_patients;
END$$

DELIMITER ;

-- --------------------------------------------------------

COMMIT;
