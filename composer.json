{"name": "healthkey/medical-system", "description": "نظام إدارة السجلات الطبية الإلكترونية - HealthKey", "type": "project", "version": "1.0.0", "authors": [{"name": "HealthKey Team", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "ext-pdo": "*", "ext-mbstring": "*", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"App\\": "app/", "App\\Core\\": "app/core/", "App\\Controllers\\": "app/controllers/", "App\\Models\\": "app/models/", "App\\Helpers\\": "app/helpers/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "serve": "php -S localhost:8000 -t public"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}