<?php

/**
 * متحكم المصادقة
 * يعالج عمليات تسجيل الدخول، الخروج، وإنشاء حساب جديد
 */
class AuthController extends Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * عرض صفحة تسجيل الدخول
     */
    public function login()
    {
        // إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه لصفحته
        if ($this->isLoggedIn()) {
            $this->redirectToDashboard();
            return;
        }

        $data = [
            'title' => 'تسجيل الدخول',
            'errors' => [],
            'old_input' => []
        ];

        // معالجة طلب تسجيل الدخول
        if (App::isPost()) {
            $this->processLogin();
            return;
        }

        $this->view('auth/login', $data, 'auth');
    }

    /**
     * معالجة طلب تسجيل الدخول
     */
    private function processLogin()
    {
        $email = App::post('email');
        $password = App::post('password');
        $remember = App::post('remember');

        // التحقق من صحة البيانات
        $errors = $this->validate([
            'email' => $email,
            'password' => $password
        ], [
            'email' => [
                'required' => true,
                'email' => true,
                'message' => 'البريد الإلكتروني مطلوب ويجب أن يكون صحيحاً'
            ],
            'password' => [
                'required' => true,
                'min_length' => 6,
                'message' => 'كلمة المرور مطلوبة ويجب أن تكون 6 أحرف على الأقل'
            ]
        ]);

        if (!empty($errors)) {
            $this->view('auth/login', [
                'title' => 'تسجيل الدخول',
                'errors' => $errors,
                'old_input' => ['email' => $email]
            ], 'auth');
            return;
        }

        // البحث عن المستخدم
        $user = $this->db->selectOne(
            "SELECT * FROM users WHERE email = :email AND is_active = 1",
            [':email' => $email]
        );

        if (!$user || !password_verify($password, $user['password'])) {
            $this->view('auth/login', [
                'title' => 'تسجيل الدخول',
                'errors' => ['login' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'],
                'old_input' => ['email' => $email]
            ], 'auth');
            return;
        }

        // تسجيل دخول المستخدم
        $this->loginUser($user);

        // تذكر المستخدم إذا طلب ذلك
        if ($remember) {
            $this->setRememberToken($user['id']);
        }

        // تسجيل النشاط
        $this->logActivity('login', 'تسجيل دخول ناجح');

        // إعادة التوجيه لصفحة المستخدم
        $this->redirectToDashboard();
    }

    /**
     * عرض صفحة التسجيل
     */
    public function register()
    {
        // إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه لصفحته
        if ($this->isLoggedIn()) {
            $this->redirectToDashboard();
            return;
        }

        $data = [
            'title' => 'إنشاء حساب جديد',
            'errors' => [],
            'old_input' => []
        ];

        // معالجة طلب التسجيل
        if (App::isPost()) {
            $this->processRegistration();
            return;
        }

        $this->view('auth/register', $data, 'auth');
    }

    /**
     * معالجة طلب التسجيل
     */
    private function processRegistration()
    {
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->validate($postData, [
            'first_name' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 50,
                'message' => 'الاسم الأول مطلوب ويجب أن يكون بين 2-50 حرف'
            ],
            'last_name' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 50,
                'message' => 'الاسم الأخير مطلوب ويجب أن يكون بين 2-50 حرف'
            ],
            'email' => [
                'required' => true,
                'email' => true,
                'message' => 'البريد الإلكتروني مطلوب ويجب أن يكون صحيحاً'
            ],
            'password' => [
                'required' => true,
                'min_length' => 8,
                'message' => 'كلمة المرور مطلوبة ويجب أن تكون 8 أحرف على الأقل'
            ],
            'confirm_password' => [
                'required' => true,
                'match' => 'password',
                'message' => 'تأكيد كلمة المرور مطلوب ويجب أن يطابق كلمة المرور'
            ],
            'phone' => [
                'required' => true,
                'message' => 'رقم الهاتف مطلوب'
            ],
            'user_type' => [
                'required' => true,
                'message' => 'نوع المستخدم مطلوب'
            ]
        ]);

        // التحقق من عدم وجود البريد الإلكتروني مسبقاً
        if (empty($errors['email'])) {
            $existingUser = $this->db->selectOne(
                "SELECT id FROM users WHERE email = :email",
                [':email' => $postData['email']]
            );

            if ($existingUser) {
                $errors['email'] = 'البريد الإلكتروني مستخدم بالفعل';
            }
        }

        // التحقق من الرقم الوطني إذا تم إدخاله
        if (!empty($postData['national_id'])) {
            $existingNationalId = $this->db->selectOne(
                "SELECT id FROM users WHERE national_id = :national_id",
                [':national_id' => $postData['national_id']]
            );

            if ($existingNationalId) {
                $errors['national_id'] = 'الرقم الوطني مستخدم بالفعل';
            }
        }

        if (!empty($errors)) {
            $this->view('auth/register', [
                'title' => 'إنشاء حساب جديد',
                'errors' => $errors,
                'old_input' => $postData
            ], 'auth');
            return;
        }

        // إنشاء المستخدم الجديد
        $userId = $this->createUser($postData);

        if ($userId) {
            // إنشاء معلومات إضافية حسب نوع المستخدم
            $this->createUserProfile($userId, $postData);

            // تسجيل دخول المستخدم الجديد
            $newUser = $this->db->selectOne(
                "SELECT * FROM users WHERE id = :id",
                [':id' => $userId]
            );

            $this->loginUser($newUser);

            // تسجيل النشاط
            $this->logActivity('register', 'إنشاء حساب جديد');

            $this->setFlashMessage('تم إنشاء حسابك بنجاح! مرحباً بك في ' . APP_NAME, 'success');
            $this->redirectToDashboard();
        } else {
            $this->setFlashMessage('حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.', 'error');
            $this->view('auth/register', [
                'title' => 'إنشاء حساب جديد',
                'errors' => [],
                'old_input' => $postData
            ], 'auth');
        }
    }

    /**
     * إنشاء مستخدم جديد
     */
    private function createUser($data)
    {
        $query = "INSERT INTO users (email, password, user_type, first_name, last_name, phone, 
                                   date_of_birth, gender, national_id, address, created_at) 
                  VALUES (:email, :password, :user_type, :first_name, :last_name, :phone, 
                          :date_of_birth, :gender, :national_id, :address, NOW())";

        $params = [
            ':email' => $data['email'],
            ':password' => password_hash($data['password'], PASSWORD_DEFAULT),
            ':user_type' => $data['user_type'],
            ':first_name' => $data['first_name'],
            ':last_name' => $data['last_name'],
            ':phone' => $data['phone'],
            ':date_of_birth' => $data['date_of_birth'] ?? null,
            ':gender' => $data['gender'] ?? null,
            ':national_id' => $data['national_id'] ?? null,
            ':address' => $data['address'] ?? null
        ];

        return $this->db->insert($query, $params);
    }

    /**
     * إنشاء ملف تعريفي إضافي حسب نوع المستخدم
     */
    private function createUserProfile($userId, $data)
    {
        switch ($data['user_type']) {
            case 'doctor':
                if (!empty($data['license_number']) && !empty($data['specialization'])) {
                    $this->db->insert(
                        "INSERT INTO doctors (user_id, license_number, specialization, years_of_experience, created_at) 
                         VALUES (:user_id, :license_number, :specialization, :years_of_experience, NOW())",
                        [
                            ':user_id' => $userId,
                            ':license_number' => $data['license_number'],
                            ':specialization' => $data['specialization'],
                            ':years_of_experience' => $data['years_of_experience'] ?? 0
                        ]
                    );
                }
                break;

            case 'pharmacist':
                if (!empty($data['license_number']) && !empty($data['pharmacy_name'])) {
                    $this->db->insert(
                        "INSERT INTO pharmacists (user_id, license_number, pharmacy_name, pharmacy_address, created_at) 
                         VALUES (:user_id, :license_number, :pharmacy_name, :pharmacy_address, NOW())",
                        [
                            ':user_id' => $userId,
                            ':license_number' => $data['license_number'],
                            ':pharmacy_name' => $data['pharmacy_name'],
                            ':pharmacy_address' => $data['pharmacy_address'] ?? null
                        ]
                    );
                }
                break;
        }
    }

    /**
     * تسجيل خروج المستخدم
     */
    public function logout()
    {
        if ($this->isLoggedIn()) {
            // تسجيل النشاط
            $this->logActivity('logout', 'تسجيل خروج');

            // حذف رمز التذكر إن وجد
            $this->clearRememberToken();

            // تسجيل خروج المستخدم
            $this->logoutUser();

            $this->setFlashMessage('تم تسجيل خروجك بنجاح', 'success');
        }

        $this->redirect('');
    }

    /**
     * إعادة التوجيه لصفحة المستخدم المناسبة
     */
    private function redirectToDashboard()
    {
        if (!$this->currentUser) {
            $this->redirect('');
            return;
        }

        $userType = $this->currentUser['user_type'];
        $this->redirect($userType . '/dashboard');
    }

    /**
     * تعيين رمز التذكر
     */
    private function setRememberToken($userId)
    {
        $token = bin2hex(random_bytes(32));
        $expiry = date('Y-m-d H:i:s', strtotime('+30 days'));

        // حفظ الرمز في قاعدة البيانات
        $this->db->insert(
            "INSERT INTO user_sessions (id, user_id, ip_address, user_agent, created_at) 
             VALUES (:token, :user_id, :ip_address, :user_agent, NOW()) 
             ON DUPLICATE KEY UPDATE last_activity = NOW()",
            [
                ':token' => $token,
                ':user_id' => $userId,
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]
        );

        // تعيين الكوكي
        setcookie('remember_token', $token, strtotime('+30 days'), '/', '', false, true);
    }

    /**
     * مسح رمز التذكر
     */
    private function clearRememberToken()
    {
        if (isset($_COOKIE['remember_token'])) {
            // حذف من قاعدة البيانات
            $this->db->delete(
                "DELETE FROM user_sessions WHERE id = :token",
                [':token' => $_COOKIE['remember_token']]
            );

            // حذف الكوكي
            setcookie('remember_token', '', time() - 3600, '/');
        }
    }

    /**
     * التحقق من رمز التذكر
     */
    public function checkRememberToken()
    {
        if (!$this->isLoggedIn() && isset($_COOKIE['remember_token'])) {
            $session = $this->db->selectOne(
                "SELECT us.*, u.* FROM user_sessions us 
                 JOIN users u ON us.user_id = u.id 
                 WHERE us.id = :token AND u.is_active = 1",
                [':token' => $_COOKIE['remember_token']]
            );

            if ($session) {
                // تحديث آخر نشاط
                $this->db->update(
                    "UPDATE user_sessions SET last_activity = NOW() WHERE id = :token",
                    [':token' => $_COOKIE['remember_token']]
                );

                // تسجيل دخول المستخدم
                $this->loginUser($session);
                return true;
            } else {
                // حذف الكوكي غير الصالح
                setcookie('remember_token', '', time() - 3600, '/');
            }
        }

        return false;
    }
}
