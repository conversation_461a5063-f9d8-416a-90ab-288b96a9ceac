<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">لوحة تحكم المدير</h1>
        <p class="text-muted">نظرة شاملة على النظام والإحصائيات</p>
    </div>
    <div>
        <?php if ($unreadNotifications > 0): ?>
            <a href="<?= App::url('admin/notifications') ?>" class="btn btn-outline-primary position-relative">
                <i class="bi bi-bell"></i>
                الإشعارات
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    <?= $unreadNotifications ?>
                </span>
            </a>
        <?php endif; ?>
        <a href="<?= App::url('admin/backup') ?>" class="btn btn-outline-warning ms-2">
            <i class="bi bi-shield-check"></i>
            النسخ الاحتياطي
        </a>
    </div>
</div>

<!-- System Health Alert -->
<?php if ($systemHealth['database']['status'] !== 'healthy'): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bi bi-exclamation-triangle-fill me-2"></i>
    <strong>تحذير!</strong> هناك مشكلة في قاعدة البيانات. يرجى التحقق من الإعدادات.
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['users']['total'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي المستخدمين</p>
                        <small class="opacity-75">
                            +<?= $stats['users']['new_this_month'] ?? 0 ?> هذا الشهر
                        </small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['appointments']['total'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي المواعيد</p>
                        <small class="opacity-75">
                            <?= $stats['appointments']['today'] ?? 0 ?> اليوم
                        </small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['prescriptions']['total'] ?? 0 ?></h4>
                        <p class="mb-0">الوصفات الطبية</p>
                        <small class="opacity-75">
                            <?= $stats['prescriptions']['active'] ?? 0 ?> نشطة
                        </small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['medical_records']['total'] ?? 0 ?></h4>
                        <p class="mb-0">السجلات الطبية</p>
                        <small class="opacity-75">
                            <?= $stats['medical_records']['this_week'] ?? 0 ?> هذا الأسبوع
                        </small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard2-pulse display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Type Distribution -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart me-2"></i>
                    توزيع المستخدمين
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <canvas id="userDistributionChart" width="400" height="200"></canvas>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-primary"><?= $stats['users']['patients'] ?? 0 ?></h4>
                                    <p class="mb-0">المرضى</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-success"><?= $stats['users']['doctors'] ?? 0 ?></h4>
                                    <p class="mb-0">الأطباء</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-info"><?= $stats['users']['pharmacists'] ?? 0 ?></h4>
                                    <p class="mb-0">الصيادلة</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-warning"><?= $stats['users']['admins'] ?? 0 ?></h4>
                                    <p class="mb-0">المديرين</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Health -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity me-2"></i>
                    حالة النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>قاعدة البيانات</span>
                        <span class="badge bg-<?= $systemHealth['database']['status'] === 'healthy' ? 'success' : 'danger' ?>">
                            <?= $systemHealth['database']['status'] === 'healthy' ? 'سليمة' : 'خطأ' ?>
                        </span>
                    </div>
                    <small class="text-muted">وقت الاستجابة: <?= $systemHealth['database']['response_time'] ?></small>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>التخزين</span>
                        <span class="badge bg-success">سليم</span>
                    </div>
                    <small class="text-muted">مساحة متاحة: <?= $systemHealth['storage']['free_space'] ?></small>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>الذاكرة</span>
                        <span class="badge bg-info">عادي</span>
                    </div>
                    <small class="text-muted">الاستخدام: <?= $systemHealth['memory']['usage'] ?></small>
                </div>
                
                <div>
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>وقت التشغيل</span>
                        <span class="badge bg-primary">نشط</span>
                    </div>
                    <small class="text-muted"><?= $systemHealth['uptime'] ?></small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Users -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-person-plus me-2"></i>
                    المستخدمين الجدد
                </h5>
                <a href="<?= App::url('admin/users') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentUsers)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($recentUsers, 0, 5) as $user): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
                                                    <i class="bi bi-person text-muted"></i>
                                                </div>
                                                <div>
                                                    <strong><?= User::getFullName($user) ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?= htmlspecialchars($user['email']) ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= User::getTypeColor($user['user_type']) ?>">
                                                <?= User::getTypeLabel($user['user_type']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?= DateHelper::formatArabic($user['created_at'], 'short') ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                                <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-person-x display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مستخدمين جدد</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Appointments -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-event me-2"></i>
                    المواعيد الحديثة
                </h5>
                <a href="<?= App::url('admin/appointments') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentAppointments)): ?>
                    <?php foreach (array_slice($recentAppointments, 0, 5) as $appointment): ?>
                        <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <?= htmlspecialchars($appointment['patient_name'] ?? 'غير محدد') ?>
                                    <i class="bi bi-arrow-left-right mx-2 text-muted"></i>
                                    د. <?= htmlspecialchars($appointment['doctor_name'] ?? 'غير محدد') ?>
                                </h6>
                                <p class="mb-0 text-muted small">
                                    <i class="bi bi-calendar me-1"></i>
                                    <?= DateHelper::formatArabic($appointment['appointment_date'] ?? '') ?>
                                    <i class="bi bi-clock me-1 ms-2"></i>
                                    <?= DateHelper::formatTime($appointment['appointment_time'] ?? '') ?>
                                </p>
                            </div>
                            <div>
                                <span class="badge bg-<?= Appointment::getStatusColor($appointment['status'] ?? 'scheduled') ?>">
                                    <?= Appointment::getStatusLabel($appointment['status'] ?? 'scheduled') ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-calendar-x display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مواعيد حديثة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/add-user') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-person-plus display-6 mb-2"></i>
                            <span class="small">إضافة مستخدم</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/users') ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-people display-6 mb-2"></i>
                            <span class="small">إدارة المستخدمين</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/appointments') ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-calendar-check display-6 mb-2"></i>
                            <span class="small">إدارة المواعيد</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/reports') ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-graph-up display-6 mb-2"></i>
                            <span class="small">التقارير</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/notifications') ?>" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-bell display-6 mb-2"></i>
                            <span class="small">الإشعارات</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/settings') ?>" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-gear display-6 mb-2"></i>
                            <span class="small">الإعدادات</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// User Distribution Chart
const ctx = document.getElementById('userDistributionChart').getContext('2d');
const userDistributionChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['المرضى', 'الأطباء', 'الصيادلة', 'المديرين'],
        datasets: [{
            data: [
                <?= $stats['users']['patients'] ?? 0 ?>,
                <?= $stats['users']['doctors'] ?? 0 ?>,
                <?= $stats['users']['pharmacists'] ?? 0 ?>,
                <?= $stats['users']['admins'] ?? 0 ?>
            ],
            backgroundColor: [
                '#0d6efd',
                '#198754',
                '#0dcaf0',
                '#ffc107'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});
</script>
