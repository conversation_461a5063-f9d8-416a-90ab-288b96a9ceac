<?php

/**
 * مساعد إدارة الروابط والتوجيه
 * يوفر وظائف لإنشاء الروابط وإدارة التوجيه والمعاملات
 */
class UrlHelper
{
    /**
     * إنشاء رابط كامل
     */
    public static function url($path = '', $params = [])
    {
        $baseUrl = rtrim(APP_URL, '/');
        $path = ltrim($path, '/');
        
        $url = $baseUrl;
        if (!empty($path)) {
            $url .= '/' . $path;
        }
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }

    /**
     * إنشاء رابط للأصول الثابتة
     */
    public static function asset($path)
    {
        $baseUrl = rtrim(APP_URL, '/');
        $path = ltrim($path, '/');
        
        return $baseUrl . '/' . $path;
    }

    /**
     * إعادة التوجيه
     */
    public static function redirect($path = '', $statusCode = 302)
    {
        $url = self::url($path);
        header("Location: $url", true, $statusCode);
        exit;
    }

    /**
     * إعادة التوجيه مع رسالة
     */
    public static function redirectWithMessage($path, $message, $type = 'info')
    {
        SessionHelper::setFlash('message', $message, $type);
        self::redirect($path);
    }

    /**
     * إعادة التوجيه للخلف
     */
    public static function back($default = '')
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        
        if (!empty($referer) && self::isInternalUrl($referer)) {
            header("Location: $referer");
        } else {
            self::redirect($default);
        }
        exit;
    }

    /**
     * التحقق من كون الرابط داخلي
     */
    public static function isInternalUrl($url)
    {
        $baseUrl = parse_url(APP_URL, PHP_URL_HOST);
        $urlHost = parse_url($url, PHP_URL_HOST);
        
        return $baseUrl === $urlHost;
    }

    /**
     * الحصول على الرابط الحالي
     */
    public static function current($includeQuery = true)
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $uri = $_SERVER['REQUEST_URI'];
        
        if (!$includeQuery) {
            $uri = strtok($uri, '?');
        }
        
        return $protocol . '://' . $host . $uri;
    }

    /**
     * الحصول على المسار الحالي
     */
    public static function currentPath()
    {
        $uri = $_SERVER['REQUEST_URI'];
        $path = strtok($uri, '?');
        
        // إزالة BASE_PATH إذا كان موجوداً
        if (defined('BASE_PATH') && !empty(BASE_PATH)) {
            $basePath = rtrim(BASE_PATH, '/');
            if (strpos($path, $basePath) === 0) {
                $path = substr($path, strlen($basePath));
            }
        }
        
        return ltrim($path, '/');
    }

    /**
     * التحقق من كون المسار الحالي يطابق المسار المعطى
     */
    public static function isCurrentPath($path)
    {
        return self::currentPath() === ltrim($path, '/');
    }

    /**
     * التحقق من كون المسار الحالي يبدأ بالمسار المعطى
     */
    public static function pathStartsWith($path)
    {
        $currentPath = self::currentPath();
        $path = ltrim($path, '/');
        
        return strpos($currentPath, $path) === 0;
    }

    /**
     * إنشاء رابط مع معاملات الاستعلام الحالية
     */
    public static function urlWithQuery($path, $newParams = [])
    {
        $currentParams = $_GET;
        $params = array_merge($currentParams, $newParams);
        
        return self::url($path, $params);
    }

    /**
     * إضافة معاملات للرابط الحالي
     */
    public static function addQueryParams($params)
    {
        $currentPath = self::currentPath();
        return self::urlWithQuery($currentPath, $params);
    }

    /**
     * إزالة معاملات من الرابط الحالي
     */
    public static function removeQueryParams($keys)
    {
        $currentParams = $_GET;
        
        foreach ((array)$keys as $key) {
            unset($currentParams[$key]);
        }
        
        $currentPath = self::currentPath();
        return self::url($currentPath, $currentParams);
    }

    /**
     * إنشاء رابط للصفحة التالية في التصفح
     */
    public static function nextPage($currentPage, $totalPages, $baseUrl = null)
    {
        if ($currentPage >= $totalPages) {
            return null;
        }
        
        $baseUrl = $baseUrl ?? self::currentPath();
        $params = $_GET;
        $params['page'] = $currentPage + 1;
        
        return self::url($baseUrl, $params);
    }

    /**
     * إنشاء رابط للصفحة السابقة في التصفح
     */
    public static function prevPage($currentPage, $baseUrl = null)
    {
        if ($currentPage <= 1) {
            return null;
        }
        
        $baseUrl = $baseUrl ?? self::currentPath();
        $params = $_GET;
        $params['page'] = $currentPage - 1;
        
        return self::url($baseUrl, $params);
    }

    /**
     * إنشاء روابط التصفح
     */
    public static function pagination($currentPage, $totalPages, $baseUrl = null, $range = 2)
    {
        $baseUrl = $baseUrl ?? self::currentPath();
        $params = $_GET;
        unset($params['page']); // إزالة معامل الصفحة الحالي
        
        $pagination = [
            'current' => $currentPage,
            'total' => $totalPages,
            'prev' => null,
            'next' => null,
            'pages' => []
        ];
        
        // الصفحة السابقة
        if ($currentPage > 1) {
            $prevParams = array_merge($params, ['page' => $currentPage - 1]);
            $pagination['prev'] = self::url($baseUrl, $prevParams);
        }
        
        // الصفحة التالية
        if ($currentPage < $totalPages) {
            $nextParams = array_merge($params, ['page' => $currentPage + 1]);
            $pagination['next'] = self::url($baseUrl, $nextParams);
        }
        
        // الصفحات
        $start = max(1, $currentPage - $range);
        $end = min($totalPages, $currentPage + $range);
        
        for ($i = $start; $i <= $end; $i++) {
            $pageParams = array_merge($params, ['page' => $i]);
            $pagination['pages'][] = [
                'number' => $i,
                'url' => self::url($baseUrl, $pageParams),
                'current' => $i === $currentPage
            ];
        }
        
        return $pagination;
    }

    /**
     * إنشاء رابط للترتيب
     */
    public static function sortUrl($column, $currentSort = null, $currentDirection = 'asc')
    {
        $params = $_GET;
        $params['sort'] = $column;
        
        // تحديد اتجاه الترتيب
        if ($currentSort === $column && $currentDirection === 'asc') {
            $params['direction'] = 'desc';
        } else {
            $params['direction'] = 'asc';
        }
        
        return self::url(self::currentPath(), $params);
    }

    /**
     * إنشاء رابط للبحث
     */
    public static function searchUrl($query, $filters = [])
    {
        $params = array_merge($_GET, $filters, ['q' => $query]);
        unset($params['page']); // إعادة تعيين الصفحة عند البحث
        
        return self::url(self::currentPath(), $params);
    }

    /**
     * إنشاء رابط للتصفية
     */
    public static function filterUrl($filters)
    {
        $params = array_merge($_GET, $filters);
        unset($params['page']); // إعادة تعيين الصفحة عند التصفية
        
        return self::url(self::currentPath(), $params);
    }

    /**
     * تنظيف الرابط من المعاملات الفارغة
     */
    public static function cleanUrl($url)
    {
        $parts = parse_url($url);
        
        if (isset($parts['query'])) {
            parse_str($parts['query'], $params);
            $params = array_filter($params, function($value) {
                return $value !== '' && $value !== null;
            });
            
            $parts['query'] = http_build_query($params);
            if (empty($parts['query'])) {
                unset($parts['query']);
            }
        }
        
        return self::buildUrl($parts);
    }

    /**
     * بناء رابط من أجزاء
     */
    private static function buildUrl($parts)
    {
        $url = '';
        
        if (isset($parts['scheme'])) {
            $url .= $parts['scheme'] . '://';
        }
        
        if (isset($parts['host'])) {
            $url .= $parts['host'];
        }
        
        if (isset($parts['port'])) {
            $url .= ':' . $parts['port'];
        }
        
        if (isset($parts['path'])) {
            $url .= $parts['path'];
        }
        
        if (isset($parts['query'])) {
            $url .= '?' . $parts['query'];
        }
        
        if (isset($parts['fragment'])) {
            $url .= '#' . $parts['fragment'];
        }
        
        return $url;
    }

    /**
     * تشفير معاملات الرابط
     */
    public static function encodeParams($params)
    {
        $encoded = [];
        foreach ($params as $key => $value) {
            $encoded[urlencode($key)] = urlencode($value);
        }
        return $encoded;
    }

    /**
     * فك تشفير معاملات الرابط
     */
    public static function decodeParams($params)
    {
        $decoded = [];
        foreach ($params as $key => $value) {
            $decoded[urldecode($key)] = urldecode($value);
        }
        return $decoded;
    }

    /**
     * إنشاء رابط آمن مع رمز CSRF
     */
    public static function secureUrl($path, $params = [])
    {
        $params['csrf_token'] = SessionHelper::generateCSRFToken();
        return self::url($path, $params);
    }

    /**
     * إنشاء رابط للتحميل
     */
    public static function downloadUrl($file, $name = null)
    {
        $params = ['file' => $file];
        if ($name) {
            $params['name'] = $name;
        }
        
        return self::url('download', $params);
    }

    /**
     * إنشاء رابط للصورة المصغرة
     */
    public static function thumbnailUrl($image, $width = 150, $height = 150)
    {
        return self::url('thumbnail', [
            'image' => $image,
            'w' => $width,
            'h' => $height
        ]);
    }

    /**
     * إنشاء رابط API
     */
    public static function apiUrl($endpoint, $version = 'v1')
    {
        return self::url("api/$version/$endpoint");
    }

    /**
     * إنشاء رابط للمشاركة الاجتماعية
     */
    public static function socialShareUrl($platform, $url, $text = '')
    {
        $url = urlencode($url);
        $text = urlencode($text);
        
        switch (strtolower($platform)) {
            case 'facebook':
                return "https://www.facebook.com/sharer/sharer.php?u=$url";
                
            case 'twitter':
                return "https://twitter.com/intent/tweet?url=$url&text=$text";
                
            case 'linkedin':
                return "https://www.linkedin.com/sharing/share-offsite/?url=$url";
                
            case 'whatsapp':
                return "https://wa.me/?text=$text%20$url";
                
            case 'telegram':
                return "https://t.me/share/url?url=$url&text=$text";
                
            default:
                return '#';
        }
    }

    /**
     * الحصول على معاملات الاستعلام الحالية
     */
    public static function getQueryParams()
    {
        return $_GET;
    }

    /**
     * الحصول على معامل استعلام محدد
     */
    public static function getQueryParam($key, $default = null)
    {
        return $_GET[$key] ?? $default;
    }

    /**
     * التحقق من وجود معامل استعلام
     */
    public static function hasQueryParam($key)
    {
        return isset($_GET[$key]);
    }

    /**
     * إنشاء breadcrumb
     */
    public static function breadcrumb($items)
    {
        $breadcrumb = [];
        
        foreach ($items as $item) {
            if (is_string($item)) {
                $breadcrumb[] = ['title' => $item, 'url' => null];
            } elseif (is_array($item) && isset($item['title'])) {
                $breadcrumb[] = [
                    'title' => $item['title'],
                    'url' => $item['url'] ?? null
                ];
            }
        }
        
        return $breadcrumb;
    }

    /**
     * تحويل المسار النسبي إلى مطلق
     */
    public static function absolute($path)
    {
        if (strpos($path, 'http') === 0) {
            return $path; // الرابط مطلق بالفعل
        }
        
        return self::url($path);
    }

    /**
     * إنشاء رابط مع معرف فريد لمنع التخزين المؤقت
     */
    public static function urlWithTimestamp($path)
    {
        return self::url($path, ['t' => time()]);
    }

    /**
     * إنشاء رابط للطباعة
     */
    public static function printUrl($path = null)
    {
        $path = $path ?? self::currentPath();
        return self::url($path, ['print' => 1]);
    }

    /**
     * إنشاء رابط للتصدير
     */
    public static function exportUrl($format = 'pdf', $path = null)
    {
        $path = $path ?? self::currentPath();
        return self::url($path, ['export' => $format]);
    }
}
