<?php

/**
 * نموذج الوصفة الطبية
 * يتعامل مع جدول الوصفات والأدوية المرتبطة بها
 */
class Prescription
{
    private $db;
    private $table = 'prescriptions';
    private $medicationsTable = 'prescription_medications';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على وصفة بواسطة ID
     */
    public function findById($id)
    {
        $query = "SELECT p.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name,
                         d.specialization as doctor_specialization
                  FROM {$this->table} p
                  LEFT JOIN users patient ON p.patient_id = patient.id
                  LEFT JOIN users doctor ON p.doctor_id = doctor.id
                  LEFT JOIN doctors d ON doctor.id = d.user_id
                  WHERE p.id = :id";
        
        return $this->db->selectOne($query, [':id' => $id]);
    }

    /**
     * الحصول على وصفة بواسطة الكود
     */
    public function findByCode($code)
    {
        $query = "SELECT p.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name,
                         d.specialization as doctor_specialization
                  FROM {$this->table} p
                  LEFT JOIN users patient ON p.patient_id = patient.id
                  LEFT JOIN users doctor ON p.doctor_id = doctor.id
                  LEFT JOIN doctors d ON doctor.id = d.user_id
                  WHERE p.prescription_code = :code";
        
        return $this->db->selectOne($query, [':code' => $code]);
    }

    /**
     * إنشاء وصفة جديدة
     */
    public function create($data)
    {
        // إنشاء كود فريد للوصفة
        $prescriptionCode = $this->generatePrescriptionCode();

        $query = "INSERT INTO {$this->table} (patient_id, doctor_id, prescription_code, diagnosis, 
                                            notes, issue_date, expiry_date, created_at) 
                  VALUES (:patient_id, :doctor_id, :prescription_code, :diagnosis, 
                          :notes, :issue_date, :expiry_date, NOW())";

        $params = [
            ':patient_id' => $data['patient_id'],
            ':doctor_id' => $data['doctor_id'],
            ':prescription_code' => $prescriptionCode,
            ':diagnosis' => $data['diagnosis'] ?? null,
            ':notes' => $data['notes'] ?? null,
            ':issue_date' => $data['issue_date'] ?? date('Y-m-d'),
            ':expiry_date' => $data['expiry_date'] ?? date('Y-m-d', strtotime('+30 days'))
        ];

        $prescriptionId = $this->db->insert($query, $params);

        if ($prescriptionId && !empty($data['medications'])) {
            $this->addMedications($prescriptionId, $data['medications']);
        }

        return $prescriptionId;
    }

    /**
     * تحديث الوصفة
     */
    public function update($id, $data)
    {
        $fields = [];
        $params = [':id' => $id];

        $allowedFields = ['diagnosis', 'notes', 'status', 'expiry_date'];
        
        foreach ($data as $key => $value) {
            if (in_array($key, $allowedFields)) {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if (empty($fields)) {
            return false;
        }

        $query = "UPDATE {$this->table} SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = :id";
        return $this->db->update($query, $params) > 0;
    }

    /**
     * حذف الوصفة
     */
    public function delete($id)
    {
        // حذف الأدوية أولاً
        $this->db->delete("DELETE FROM {$this->medicationsTable} WHERE prescription_id = :id", [':id' => $id]);
        
        // حذف الوصفة
        $query = "DELETE FROM {$this->table} WHERE id = :id";
        return $this->db->delete($query, [':id' => $id]) > 0;
    }

    /**
     * الحصول على وصفات المريض
     */
    public function getByPatient($patientId, $limit = null)
    {
        $query = "SELECT p.*, 
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name,
                         d.specialization as doctor_specialization
                  FROM {$this->table} p
                  LEFT JOIN users doctor ON p.doctor_id = doctor.id
                  LEFT JOIN doctors d ON doctor.id = d.user_id
                  WHERE p.patient_id = :patient_id
                  ORDER BY p.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        return $this->db->select($query, [':patient_id' => $patientId]);
    }

    /**
     * الحصول على وصفات الطبيب
     */
    public function getByDoctor($doctorId, $limit = null)
    {
        $query = "SELECT p.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name
                  FROM {$this->table} p
                  LEFT JOIN users patient ON p.patient_id = patient.id
                  WHERE p.doctor_id = :doctor_id
                  ORDER BY p.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        return $this->db->select($query, [':doctor_id' => $doctorId]);
    }

    /**
     * البحث في الوصفات
     */
    public function search($term, $filters = [])
    {
        $query = "SELECT p.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name
                  FROM {$this->table} p
                  LEFT JOIN users patient ON p.patient_id = patient.id
                  LEFT JOIN users doctor ON p.doctor_id = doctor.id
                  WHERE p.prescription_code LIKE :term";
        
        $params = [':term' => "%$term%"];

        // تطبيق المرشحات
        if (!empty($filters['status'])) {
            $query .= " AND p.status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($filters['doctor_id'])) {
            $query .= " AND p.doctor_id = :doctor_id";
            $params[':doctor_id'] = $filters['doctor_id'];
        }

        if (!empty($filters['patient_id'])) {
            $query .= " AND p.patient_id = :patient_id";
            $params[':patient_id'] = $filters['patient_id'];
        }

        $query .= " ORDER BY p.created_at DESC LIMIT 20";

        return $this->db->select($query, $params);
    }

    /**
     * إضافة أدوية للوصفة
     */
    public function addMedications($prescriptionId, $medications)
    {
        foreach ($medications as $medication) {
            $query = "INSERT INTO {$this->medicationsTable} 
                      (prescription_id, medication_name, dosage, frequency, duration, instructions, quantity, created_at) 
                      VALUES (:prescription_id, :medication_name, :dosage, :frequency, :duration, :instructions, :quantity, NOW())";

            $params = [
                ':prescription_id' => $prescriptionId,
                ':medication_name' => $medication['medication_name'],
                ':dosage' => $medication['dosage'],
                ':frequency' => $medication['frequency'],
                ':duration' => $medication['duration'],
                ':instructions' => $medication['instructions'] ?? null,
                ':quantity' => $medication['quantity']
            ];

            $this->db->insert($query, $params);
        }
    }

    /**
     * الحصول على أدوية الوصفة
     */
    public function getMedications($prescriptionId)
    {
        $query = "SELECT * FROM {$this->medicationsTable} WHERE prescription_id = :prescription_id ORDER BY id";
        return $this->db->select($query, [':prescription_id' => $prescriptionId]);
    }

    /**
     * تحديث حالة صرف الدواء
     */
    public function dispenseMedication($medicationId, $pharmacistId, $dispensedQuantity = null)
    {
        $medication = $this->db->selectOne(
            "SELECT * FROM {$this->medicationsTable} WHERE id = :id",
            [':id' => $medicationId]
        );

        if (!$medication) {
            return false;
        }

        $dispensedQty = $dispensedQuantity ?? $medication['quantity'];

        $query = "UPDATE {$this->medicationsTable} 
                  SET dispensed_quantity = :dispensed_quantity, 
                      is_dispensed = 1, 
                      dispensed_at = NOW(), 
                      dispensed_by = :dispensed_by 
                  WHERE id = :id";

        $params = [
            ':id' => $medicationId,
            ':dispensed_quantity' => $dispensedQty,
            ':dispensed_by' => $pharmacistId
        ];

        $result = $this->db->update($query, $params) > 0;

        // التحقق من صرف جميع الأدوية لتحديث حالة الوصفة
        if ($result) {
            $this->updatePrescriptionStatus($medication['prescription_id']);
        }

        return $result;
    }

    /**
     * تحديث حالة الوصفة بناءً على حالة الأدوية
     */
    private function updatePrescriptionStatus($prescriptionId)
    {
        $medications = $this->getMedications($prescriptionId);
        $allDispensed = true;

        foreach ($medications as $medication) {
            if (!$medication['is_dispensed']) {
                $allDispensed = false;
                break;
            }
        }

        if ($allDispensed) {
            $this->update($prescriptionId, ['status' => 'dispensed']);
        }
    }

    /**
     * إنشاء كود فريد للوصفة
     */
    private function generatePrescriptionCode()
    {
        do {
            $code = 'RX' . date('Y') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
            $exists = $this->findByCode($code);
        } while ($exists);

        return $code;
    }

    /**
     * الحصول على الوصفات النشطة
     */
    public function getActive($limit = null)
    {
        $query = "SELECT p.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name
                  FROM {$this->table} p
                  LEFT JOIN users patient ON p.patient_id = patient.id
                  LEFT JOIN users doctor ON p.doctor_id = doctor.id
                  WHERE p.status = 'active' AND p.expiry_date >= CURDATE()
                  ORDER BY p.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        return $this->db->select($query);
    }

    /**
     * الحصول على الوصفات المنتهية الصلاحية
     */
    public function getExpired()
    {
        $query = "SELECT p.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name
                  FROM {$this->table} p
                  LEFT JOIN users patient ON p.patient_id = patient.id
                  LEFT JOIN users doctor ON p.doctor_id = doctor.id
                  WHERE p.status = 'active' AND p.expiry_date < CURDATE()
                  ORDER BY p.expiry_date DESC";

        return $this->db->select($query);
    }

    /**
     * إحصائيات الوصفات
     */
    public function getStats($doctorId = null)
    {
        $stats = [];
        $whereClause = $doctorId ? "WHERE doctor_id = :doctor_id" : "";
        $params = $doctorId ? [':doctor_id' => $doctorId] : [];

        // إجمالي الوصفات
        $total = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->table} $whereClause", $params);
        $stats['total'] = (int)$total['count'];

        // الوصفات حسب الحالة
        $byStatus = $this->db->select(
            "SELECT status, COUNT(*) as count FROM {$this->table} $whereClause GROUP BY status",
            $params
        );

        foreach ($byStatus as $status) {
            $stats[$status['status']] = (int)$status['count'];
        }

        // الوصفات هذا الشهر
        $thisMonth = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW()) 
             " . ($doctorId ? "AND doctor_id = :doctor_id" : ""),
            $params
        );
        $stats['this_month'] = (int)$thisMonth['count'];

        return $stats;
    }

    /**
     * التحقق من التفاعلات الدوائية
     */
    public function checkDrugInteractions($medications)
    {
        // قائمة بسيطة للتفاعلات الدوائية الشائعة
        $interactions = [
            'warfarin' => ['aspirin', 'ibuprofen'],
            'aspirin' => ['warfarin', 'methotrexate'],
            'metformin' => ['alcohol'],
            'digoxin' => ['amiodarone', 'verapamil']
        ];

        $warnings = [];
        $medicationNames = array_map('strtolower', array_column($medications, 'medication_name'));

        foreach ($medicationNames as $med1) {
            if (isset($interactions[$med1])) {
                foreach ($interactions[$med1] as $interactingDrug) {
                    if (in_array($interactingDrug, $medicationNames)) {
                        $warnings[] = "تحذير: تفاعل محتمل بين $med1 و $interactingDrug";
                    }
                }
            }
        }

        return $warnings;
    }

    /**
     * الحصول على تقرير الوصفات
     */
    public function getReport($startDate, $endDate, $doctorId = null)
    {
        $query = "SELECT p.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name,
                         COUNT(pm.id) as medication_count
                  FROM {$this->table} p
                  LEFT JOIN users patient ON p.patient_id = patient.id
                  LEFT JOIN users doctor ON p.doctor_id = doctor.id
                  LEFT JOIN {$this->medicationsTable} pm ON p.id = pm.prescription_id
                  WHERE p.created_at BETWEEN :start_date AND :end_date";
        
        $params = [
            ':start_date' => $startDate,
            ':end_date' => $endDate
        ];

        if ($doctorId) {
            $query .= " AND p.doctor_id = :doctor_id";
            $params[':doctor_id'] = $doctorId;
        }

        $query .= " GROUP BY p.id ORDER BY p.created_at DESC";

        return $this->db->select($query, $params);
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validate($data)
    {
        $errors = [];

        // التحقق من الحقول المطلوبة
        $required = ['patient_id', 'doctor_id'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[$field] = "حقل $field مطلوب";
            }
        }

        // التحقق من تاريخ الانتهاء
        if (!empty($data['expiry_date']) && strtotime($data['expiry_date']) < time()) {
            $errors['expiry_date'] = 'تاريخ انتهاء الصلاحية لا يمكن أن يكون في الماضي';
        }

        // التحقق من الأدوية
        if (empty($data['medications']) || !is_array($data['medications'])) {
            $errors['medications'] = 'يجب إضافة دواء واحد على الأقل';
        } else {
            foreach ($data['medications'] as $index => $medication) {
                if (empty($medication['medication_name'])) {
                    $errors["medications[$index][medication_name]"] = 'اسم الدواء مطلوب';
                }
                if (empty($medication['dosage'])) {
                    $errors["medications[$index][dosage]"] = 'الجرعة مطلوبة';
                }
                if (empty($medication['frequency'])) {
                    $errors["medications[$index][frequency]"] = 'عدد مرات التناول مطلوب';
                }
            }
        }

        return $errors;
    }
}
