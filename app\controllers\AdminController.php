<?php

/**
 * متحكم المدير
 * يعالج جميع العمليات الإدارية للنظام
 */
class AdminController extends Controller
{
    private $userModel;
    private $appointmentModel;
    private $prescriptionModel;
    private $medicalRecordModel;
    private $notificationModel;

    public function __construct()
    {
        parent::__construct();
        
        // التحقق من تسجيل الدخول ونوع المستخدم
        $this->requireAuth();
        $this->requireUserType('admin');
        
        // تحميل النماذج
        $this->userModel = new User();
        $this->appointmentModel = new Appointment();
        $this->prescriptionModel = new Prescription();
        $this->medicalRecordModel = new MedicalRecord();
        $this->notificationModel = new Notification();
    }

    /**
     * لوحة تحكم المدير
     */
    public function dashboard()
    {
        $data = [
            'title' => 'لوحة تحكم المدير',
            'stats' => $this->getSystemStats(),
            'recentUsers' => $this->userModel->getAll(['limit' => 10]),
            'recentAppointments' => $this->appointmentModel->search('', ['limit' => 10]),
            'systemHealth' => $this->getSystemHealth(),
            'notifications' => $this->notificationModel->getByUser($this->currentUser['id'], false, 5),
            'unreadNotifications' => $this->notificationModel->getUnreadCount($this->currentUser['id'])
        ];

        $this->view('admin/dashboard', $data);
    }

    /**
     * إدارة المستخدمين
     */
    public function users()
    {
        $page = (int)App::get('page', 1);
        $limit = 20;
        $offset = ($page - 1) * $limit;
        $userType = App::get('user_type', '');
        $search = App::get('search', '');
        $status = App::get('status', '');

        $filters = [
            'limit' => $limit,
            'offset' => $offset
        ];

        if (!empty($userType)) {
            $filters['user_type'] = $userType;
        }

        if (!empty($search)) {
            $filters['search'] = $search;
        }

        if (!empty($status)) {
            $filters['is_active'] = $status === 'active' ? 1 : 0;
        }

        $users = $this->userModel->getAll($filters);
        $totalUsers = $this->userModel->count($filters);
        $totalPages = ceil($totalUsers / $limit);

        $data = [
            'title' => 'إدارة المستخدمين',
            'users' => $users,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalUsers' => $totalUsers,
            'filters' => [
                'user_type' => $userType,
                'search' => $search,
                'status' => $status
            ],
            'pagination' => UrlHelper::pagination($page, $totalPages)
        ];

        $this->view('admin/users', $data);
    }

    /**
     * إضافة مستخدم جديد
     */
    public function addUser()
    {
        if (App::isPost()) {
            $this->processAddUser();
            return;
        }

        $data = [
            'title' => 'إضافة مستخدم جديد'
        ];

        $this->view('admin/add_user', $data);
    }

    /**
     * معالجة إضافة المستخدم
     */
    private function processAddUser()
    {
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->userModel->validate($postData);

        // التحقق من تفرد البريد الإلكتروني
        if (empty($errors['email'])) {
            $existingUser = $this->userModel->findByEmail($postData['email']);
            if ($existingUser) {
                $errors['email'] = 'البريد الإلكتروني مستخدم بالفعل';
            }
        }

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('admin/add-user');
            return;
        }

        // إنشاء المستخدم
        $userId = $this->userModel->create($postData);

        if ($userId) {
            // إنشاء ملف إضافي حسب نوع المستخدم
            $this->createAdditionalProfile($userId, $postData);

            // إرسال إشعار ترحيب
            $this->notificationModel->create(
                $userId,
                'مرحباً بك في النظام',
                'تم إنشاء حسابك بنجاح. يمكنك الآن استخدام النظام.',
                'system'
            );

            $this->setFlashMessage('تم إضافة المستخدم بنجاح', 'success');
            $this->redirect('admin/users');
        } else {
            $this->setFlashMessage('فشل في إضافة المستخدم', 'error');
            $this->redirect('admin/add-user');
        }
    }

    /**
     * تعديل المستخدم
     */
    public function editUser($userId)
    {
        $user = $this->userModel->findById($userId);
        if (!$user) {
            $this->setFlashMessage('المستخدم غير موجود', 'error');
            $this->redirect('admin/users');
            return;
        }

        if (App::isPost()) {
            $this->processEditUser($userId);
            return;
        }

        $data = [
            'title' => 'تعديل المستخدم',
            'user' => $user
        ];

        $this->view('admin/edit_user', $data);
    }

    /**
     * معالجة تعديل المستخدم
     */
    private function processEditUser($userId)
    {
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->userModel->validate($postData, true);

        // التحقق من تفرد البريد الإلكتروني
        if (empty($errors['email'])) {
            $existingUser = $this->userModel->findByEmail($postData['email']);
            if ($existingUser && $existingUser['id'] != $userId) {
                $errors['email'] = 'البريد الإلكتروني مستخدم بالفعل';
            }
        }

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('admin/edit-user/' . $userId);
            return;
        }

        // تحديث المستخدم
        if ($this->userModel->update($userId, $postData)) {
            $this->setFlashMessage('تم تحديث المستخدم بنجاح', 'success');
            $this->redirect('admin/users');
        } else {
            $this->setFlashMessage('فشل في تحديث المستخدم', 'error');
            $this->redirect('admin/edit-user/' . $userId);
        }
    }

    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggleUserStatus()
    {
        $userId = App::post('user_id');
        
        if (!$userId) {
            $this->json(['success' => false, 'message' => 'معرف المستخدم مطلوب'], 400);
            return;
        }

        $user = $this->userModel->findById($userId);
        if (!$user) {
            $this->json(['success' => false, 'message' => 'المستخدم غير موجود'], 404);
            return;
        }

        if ($this->userModel->toggleActive($userId)) {
            $status = $user['is_active'] ? 'تم إلغاء تفعيل' : 'تم تفعيل';
            $this->json(['success' => true, 'message' => "$status المستخدم بنجاح"]);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تحديث حالة المستخدم'], 500);
        }
    }

    /**
     * حذف المستخدم
     */
    public function deleteUser()
    {
        $userId = App::post('user_id');
        
        if (!$userId) {
            $this->json(['success' => false, 'message' => 'معرف المستخدم مطلوب'], 400);
            return;
        }

        $user = $this->userModel->findById($userId);
        if (!$user) {
            $this->json(['success' => false, 'message' => 'المستخدم غير موجود'], 404);
            return;
        }

        // منع حذف المدير الحالي
        if ($userId == $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'لا يمكن حذف حسابك الخاص'], 400);
            return;
        }

        if ($this->userModel->delete($userId)) {
            $this->json(['success' => true, 'message' => 'تم حذف المستخدم بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في حذف المستخدم'], 500);
        }
    }

    /**
     * إدارة المواعيد
     */
    public function appointments()
    {
        $page = (int)App::get('page', 1);
        $limit = 20;
        $offset = ($page - 1) * $limit;
        $status = App::get('status', '');
        $doctorId = App::get('doctor_id', '');
        $dateFrom = App::get('date_from', '');
        $dateTo = App::get('date_to', '');

        $filters = [];
        if (!empty($status)) $filters['status'] = $status;
        if (!empty($doctorId)) $filters['doctor_id'] = $doctorId;
        if (!empty($dateFrom)) $filters['date_from'] = $dateFrom;
        if (!empty($dateTo)) $filters['date_to'] = $dateTo;

        $appointments = $this->appointmentModel->search('', $filters);
        $doctors = $this->userModel->getActiveDoctors();

        $data = [
            'title' => 'إدارة المواعيد',
            'appointments' => array_slice($appointments, $offset, $limit),
            'doctors' => $doctors,
            'filters' => [
                'status' => $status,
                'doctor_id' => $doctorId,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ];

        $this->view('admin/appointments', $data);
    }

    /**
     * إدارة الوصفات الطبية
     */
    public function prescriptions()
    {
        $page = (int)App::get('page', 1);
        $limit = 20;
        $offset = ($page - 1) * $limit;
        $status = App::get('status', '');
        $doctorId = App::get('doctor_id', '');

        $filters = [];
        if (!empty($status)) $filters['status'] = $status;
        if (!empty($doctorId)) $filters['doctor_id'] = $doctorId;

        $prescriptions = $this->prescriptionModel->search('', $filters);
        $doctors = $this->userModel->getActiveDoctors();

        $data = [
            'title' => 'إدارة الوصفات الطبية',
            'prescriptions' => array_slice($prescriptions, $offset, $limit),
            'doctors' => $doctors,
            'filters' => [
                'status' => $status,
                'doctor_id' => $doctorId
            ]
        ];

        $this->view('admin/prescriptions', $data);
    }

    /**
     * التقارير والإحصائيات
     */
    public function reports()
    {
        $startDate = App::get('start_date', date('Y-m-01'));
        $endDate = App::get('end_date', date('Y-m-t'));
        $reportType = App::get('type', 'overview');

        $data = [
            'title' => 'التقارير والإحصائيات',
            'startDate' => $startDate,
            'endDate' => $endDate,
            'reportType' => $reportType,
            'stats' => $this->getDetailedStats($startDate, $endDate),
            'charts' => $this->getChartsData($startDate, $endDate)
        ];

        $this->view('admin/reports', $data);
    }

    /**
     * إدارة الإشعارات
     */
    public function notifications()
    {
        if (App::isPost()) {
            $this->sendBulkNotification();
            return;
        }

        $data = [
            'title' => 'إدارة الإشعارات',
            'userTypes' => ['patient' => 'المرضى', 'doctor' => 'الأطباء', 'pharmacist' => 'الصيادلة'],
            'recentNotifications' => $this->getRecentBulkNotifications()
        ];

        $this->view('admin/notifications', $data);
    }

    /**
     * إرسال إشعار جماعي
     */
    private function sendBulkNotification()
    {
        $title = App::post('title');
        $message = App::post('message');
        $userType = App::post('user_type');
        $notificationType = App::post('notification_type', 'general');

        if (empty($title) || empty($message)) {
            $this->setFlashMessage('العنوان والرسالة مطلوبان', 'error');
            $this->redirect('admin/notifications');
            return;
        }

        $sent = 0;
        if ($userType === 'all') {
            // إرسال لجميع المستخدمين
            $users = $this->userModel->getAll(['is_active' => 1]);
            $userIds = array_column($users, 'id');
            $results = $this->notificationModel->createBulk($userIds, $title, $message, $notificationType);
            $sent = count(array_filter($results));
        } else {
            // إرسال لنوع محدد من المستخدمين
            switch ($userType) {
                case 'patient':
                    $sent = count($this->notificationModel->notifyAllPatients($title, $message, $notificationType));
                    break;
                case 'doctor':
                    $sent = count($this->notificationModel->notifyAllDoctors($title, $message, $notificationType));
                    break;
                case 'pharmacist':
                    $users = $this->userModel->getAll(['user_type' => 'pharmacist', 'is_active' => 1]);
                    $userIds = array_column($users, 'id');
                    $results = $this->notificationModel->createBulk($userIds, $title, $message, $notificationType);
                    $sent = count(array_filter($results));
                    break;
            }
        }

        $this->setFlashMessage("تم إرسال الإشعار إلى $sent مستخدم", 'success');
        $this->redirect('admin/notifications');
    }

    /**
     * إعدادات النظام
     */
    public function settings()
    {
        if (App::isPost()) {
            $this->updateSettings();
            return;
        }

        $data = [
            'title' => 'إعدادات النظام',
            'settings' => $this->getSystemSettings()
        ];

        $this->view('admin/settings', $data);
    }

    /**
     * تحديث إعدادات النظام
     */
    private function updateSettings()
    {
        $settings = App::post();
        
        // حفظ الإعدادات في قاعدة البيانات أو ملف التكوين
        foreach ($settings as $key => $value) {
            $this->updateSystemSetting($key, $value);
        }

        $this->setFlashMessage('تم تحديث الإعدادات بنجاح', 'success');
        $this->redirect('admin/settings');
    }

    /**
     * سجل النشاطات
     */
    public function activityLog()
    {
        $page = (int)App::get('page', 1);
        $limit = 50;
        $offset = ($page - 1) * $limit;
        $userType = App::get('user_type', '');
        $action = App::get('action', '');
        $dateFrom = App::get('date_from', '');

        $data = [
            'title' => 'سجل النشاطات',
            'activities' => $this->getActivityLog($offset, $limit, $userType, $action, $dateFrom),
            'filters' => [
                'user_type' => $userType,
                'action' => $action,
                'date_from' => $dateFrom
            ]
        ];

        $this->view('admin/activity_log', $data);
    }

    /**
     * النسخ الاحتياطي
     */
    public function backup()
    {
        if (App::isPost()) {
            $this->createBackup();
            return;
        }

        $data = [
            'title' => 'النسخ الاحتياطي',
            'backups' => $this->getBackupList()
        ];

        $this->view('admin/backup', $data);
    }

    /**
     * إنشاء نسخة احتياطية
     */
    private function createBackup()
    {
        $backupType = App::post('backup_type', 'full');
        
        try {
            $filename = $this->performBackup($backupType);
            $this->setFlashMessage("تم إنشاء النسخة الاحتياطية: $filename", 'success');
        } catch (Exception $e) {
            $this->setFlashMessage('فشل في إنشاء النسخة الاحتياطية: ' . $e->getMessage(), 'error');
        }

        $this->redirect('admin/backup');
    }

    /**
     * الحصول على إحصائيات النظام
     */
    private function getSystemStats()
    {
        return [
            'users' => $this->userModel->getStats(),
            'appointments' => $this->appointmentModel->getStats(),
            'prescriptions' => $this->prescriptionModel->getStats(),
            'medical_records' => $this->medicalRecordModel->getStats()
        ];
    }

    /**
     * الحصول على حالة النظام
     */
    private function getSystemHealth()
    {
        return [
            'database' => $this->checkDatabaseHealth(),
            'storage' => $this->checkStorageHealth(),
            'memory' => $this->checkMemoryUsage(),
            'uptime' => $this->getSystemUptime()
        ];
    }

    /**
     * إنشاء ملف إضافي للمستخدم
     */
    private function createAdditionalProfile($userId, $data)
    {
        switch ($data['user_type']) {
            case 'doctor':
                $this->db->insert(
                    "INSERT INTO doctors (user_id, specialization, license_number, years_of_experience, created_at) 
                     VALUES (:user_id, :specialization, :license_number, :years_of_experience, NOW())",
                    [
                        ':user_id' => $userId,
                        ':specialization' => $data['specialization'] ?? null,
                        ':license_number' => $data['license_number'] ?? null,
                        ':years_of_experience' => $data['years_of_experience'] ?? null
                    ]
                );
                break;
                
            case 'pharmacist':
                $this->db->insert(
                    "INSERT INTO pharmacists (user_id, license_number, pharmacy_name, pharmacy_address, created_at) 
                     VALUES (:user_id, :license_number, :pharmacy_name, :pharmacy_address, NOW())",
                    [
                        ':user_id' => $userId,
                        ':license_number' => $data['license_number'] ?? null,
                        ':pharmacy_name' => $data['pharmacy_name'] ?? null,
                        ':pharmacy_address' => $data['pharmacy_address'] ?? null
                    ]
                );
                break;
        }
    }

    /**
     * الحصول على إحصائيات مفصلة
     */
    private function getDetailedStats($startDate, $endDate)
    {
        return [
            'new_users' => $this->getUserRegistrationStats($startDate, $endDate),
            'appointments_by_status' => $this->getAppointmentsByStatus($startDate, $endDate),
            'prescriptions_by_doctor' => $this->getPrescriptionsByDoctor($startDate, $endDate),
            'most_common_diagnoses' => $this->getMostCommonDiagnoses($startDate, $endDate)
        ];
    }

    /**
     * الحصول على بيانات الرسوم البيانية
     */
    private function getChartsData($startDate, $endDate)
    {
        return [
            'daily_appointments' => $this->getDailyAppointments($startDate, $endDate),
            'user_growth' => $this->getUserGrowthData($startDate, $endDate),
            'prescription_trends' => $this->getPrescriptionTrends($startDate, $endDate)
        ];
    }

    // المزيد من الدوال المساعدة...
    private function checkDatabaseHealth() { return ['status' => 'healthy', 'response_time' => '5ms']; }
    private function checkStorageHealth() { return ['status' => 'healthy', 'free_space' => '85%']; }
    private function checkMemoryUsage() { return ['usage' => '45%', 'available' => '4GB']; }
    private function getSystemUptime() { return '15 days, 3 hours'; }
    private function getRecentBulkNotifications() { return []; }
    private function getSystemSettings() { return []; }
    private function updateSystemSetting($key, $value) { return true; }
    private function getActivityLog($offset, $limit, $userType, $action, $dateFrom) { return []; }
    private function getBackupList() { return []; }
    private function performBackup($type) { return 'backup_' . date('Y-m-d_H-i-s') . '.sql'; }
    private function getUserRegistrationStats($startDate, $endDate) { return []; }
    private function getAppointmentsByStatus($startDate, $endDate) { return []; }
    private function getPrescriptionsByDoctor($startDate, $endDate) { return []; }
    private function getMostCommonDiagnoses($startDate, $endDate) { return []; }
    private function getDailyAppointments($startDate, $endDate) { return []; }
    private function getUserGrowthData($startDate, $endDate) { return []; }
    private function getPrescriptionTrends($startDate, $endDate) { return []; }
}
