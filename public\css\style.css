/* ملف الأنماط الرئيسي لنظام HealthKey */

/* إعدادات عامة */
:root {
    --primary-color: #2c5aa0;
    --primary-dark: #1e3f73;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* إعدادات الخط والاتجاه */
body {
    font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

/* تخصيص الألوان الأساسية */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}
.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* شريط التنقل */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

/* الشريط الجانبي */
.sidebar {
    background-color: var(--light-color);
    border-left: 1px solid #dee2e6;
    min-height: calc(100vh - 56px);
    padding: 1rem;
}

.sidebar .nav-link {
    color: var(--dark-color);
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(44, 90, 160, 0.1);
    color: var(--primary-color);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    margin-left: 0.5rem;
}

/* المحتوى الرئيسي */
.main-content {
    padding: 2rem;
    min-height: calc(100vh - 56px);
}

/* البطاقات */
.card {
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: rgba(44, 90, 160, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* الجداول */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border-top: none;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.05);
}

/* النماذج */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: var(--danger-color);
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.25rem;
}

.alert-dismissible .btn-close {
    padding: 1.25rem 1rem;
}

/* الشارات */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* إحصائيات لوحة التحكم */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.stats-card .stats-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    position: absolute;
    top: 1rem;
    left: 1rem;
}

/* قائمة المهام */
.task-list .list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.task-list .list-group-item:last-child {
    border-bottom: none;
}

.task-priority-high {
    border-right: 4px solid var(--danger-color);
}

.task-priority-medium {
    border-right: 4px solid var(--warning-color);
}

.task-priority-low {
    border-right: 4px solid var(--success-color);
}

/* التقويم */
.calendar-day {
    min-height: 100px;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
}

.calendar-day.today {
    background-color: rgba(44, 90, 160, 0.1);
}

.calendar-event {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1.5rem;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        right: -100%;
        width: 250px;
        height: calc(100vh - 56px);
        z-index: 1000;
        transition: right 0.3s ease;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .alert {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        margin: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* تخصيصات إضافية */
.text-muted {
    color: #6c757d !important;
}

.border-start {
    border-right: 1px solid #dee2e6 !important;
    border-left: none !important;
}

.border-end {
    border-left: 1px solid #dee2e6 !important;
    border-right: none !important;
}

.me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}
