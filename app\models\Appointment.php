<?php

/**
 * نموذج المواعيد
 * يتعامل مع جدول المواعيد وجميع العمليات المتعلقة بها
 */
class Appointment
{
    private $db;
    private $table = 'appointments';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على موعد بواسطة ID
     */
    public function findById($id)
    {
        $query = "SELECT a.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         patient.phone as patient_phone,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name,
                         d.specialization as doctor_specialization
                  FROM {$this->table} a
                  LEFT JOIN users patient ON a.patient_id = patient.id
                  LEFT JOIN users doctor ON a.doctor_id = doctor.id
                  LEFT JOIN doctors d ON doctor.id = d.user_id
                  WHERE a.id = :id";
        
        return $this->db->selectOne($query, [':id' => $id]);
    }

    /**
     * إنشاء موعد جديد
     */
    public function create($data)
    {
        // التحقق من توفر الموعد
        if (!$this->isTimeSlotAvailable($data['doctor_id'], $data['appointment_date'], $data['appointment_time'])) {
            return false;
        }

        $query = "INSERT INTO {$this->table} (patient_id, doctor_id, appointment_date, appointment_time, 
                                            reason, notes, status, created_at) 
                  VALUES (:patient_id, :doctor_id, :appointment_date, :appointment_time, 
                          :reason, :notes, :status, NOW())";

        $params = [
            ':patient_id' => $data['patient_id'],
            ':doctor_id' => $data['doctor_id'],
            ':appointment_date' => $data['appointment_date'],
            ':appointment_time' => $data['appointment_time'],
            ':reason' => $data['reason'] ?? null,
            ':notes' => $data['notes'] ?? null,
            ':status' => $data['status'] ?? 'scheduled'
        ];

        return $this->db->insert($query, $params);
    }

    /**
     * تحديث الموعد
     */
    public function update($id, $data)
    {
        $fields = [];
        $params = [':id' => $id];

        $allowedFields = ['appointment_date', 'appointment_time', 'reason', 'notes', 'status'];
        
        foreach ($data as $key => $value) {
            if (in_array($key, $allowedFields)) {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if (empty($fields)) {
            return false;
        }

        // التحقق من توفر الموعد الجديد إذا تم تغيير التاريخ أو الوقت
        if (isset($data['appointment_date']) || isset($data['appointment_time'])) {
            $appointment = $this->findById($id);
            $newDate = $data['appointment_date'] ?? $appointment['appointment_date'];
            $newTime = $data['appointment_time'] ?? $appointment['appointment_time'];
            
            if (!$this->isTimeSlotAvailable($appointment['doctor_id'], $newDate, $newTime, $id)) {
                return false;
            }
        }

        $query = "UPDATE {$this->table} SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = :id";
        return $this->db->update($query, $params) > 0;
    }

    /**
     * حذف الموعد
     */
    public function delete($id)
    {
        $query = "DELETE FROM {$this->table} WHERE id = :id";
        return $this->db->delete($query, [':id' => $id]) > 0;
    }

    /**
     * الحصول على مواعيد المريض
     */
    public function getByPatient($patientId, $status = null, $limit = null)
    {
        $query = "SELECT a.*, 
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name,
                         d.specialization as doctor_specialization
                  FROM {$this->table} a
                  LEFT JOIN users doctor ON a.doctor_id = doctor.id
                  LEFT JOIN doctors d ON doctor.id = d.user_id
                  WHERE a.patient_id = :patient_id";
        
        $params = [':patient_id' => $patientId];

        if ($status) {
            $query .= " AND a.status = :status";
            $params[':status'] = $status;
        }

        $query .= " ORDER BY a.appointment_date DESC, a.appointment_time DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        return $this->db->select($query, $params);
    }

    /**
     * الحصول على مواعيد الطبيب
     */
    public function getByDoctor($doctorId, $date = null, $status = null)
    {
        $query = "SELECT a.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         patient.phone as patient_phone
                  FROM {$this->table} a
                  LEFT JOIN users patient ON a.patient_id = patient.id
                  WHERE a.doctor_id = :doctor_id";
        
        $params = [':doctor_id' => $doctorId];

        if ($date) {
            $query .= " AND a.appointment_date = :date";
            $params[':date'] = $date;
        }

        if ($status) {
            $query .= " AND a.status = :status";
            $params[':status'] = $status;
        }

        $query .= " ORDER BY a.appointment_date ASC, a.appointment_time ASC";

        return $this->db->select($query, $params);
    }

    /**
     * الحصول على المواعيد القادمة
     */
    public function getUpcoming($userId, $userType, $limit = 10)
    {
        $userField = $userType === 'patient' ? 'patient_id' : 'doctor_id';
        $otherUserField = $userType === 'patient' ? 'doctor' : 'patient';
        
        $query = "SELECT a.*, 
                         CONCAT({$otherUserField}.first_name, ' ', {$otherUserField}.last_name) as other_user_name
                  FROM {$this->table} a
                  LEFT JOIN users {$otherUserField} ON a.{$otherUserField}_id = {$otherUserField}.id
                  WHERE a.{$userField} = :user_id 
                  AND (a.appointment_date > CURDATE() OR 
                       (a.appointment_date = CURDATE() AND a.appointment_time > CURTIME()))
                  AND a.status IN ('scheduled', 'confirmed')
                  ORDER BY a.appointment_date ASC, a.appointment_time ASC
                  LIMIT :limit";

        return $this->db->select($query, [':user_id' => $userId, ':limit' => $limit]);
    }

    /**
     * الحصول على المواعيد اليوم
     */
    public function getToday($doctorId)
    {
        $query = "SELECT a.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         patient.phone as patient_phone
                  FROM {$this->table} a
                  LEFT JOIN users patient ON a.patient_id = patient.id
                  WHERE a.doctor_id = :doctor_id 
                  AND a.appointment_date = CURDATE()
                  ORDER BY a.appointment_time ASC";

        return $this->db->select($query, [':doctor_id' => $doctorId]);
    }

    /**
     * التحقق من توفر الوقت
     */
    public function isTimeSlotAvailable($doctorId, $date, $time, $excludeId = null)
    {
        $query = "SELECT COUNT(*) as count FROM {$this->table} 
                  WHERE doctor_id = :doctor_id 
                  AND appointment_date = :date 
                  AND appointment_time = :time 
                  AND status NOT IN ('cancelled', 'no_show')";
        
        $params = [
            ':doctor_id' => $doctorId,
            ':date' => $date,
            ':time' => $time
        ];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeId;
        }

        $result = $this->db->selectOne($query, $params);
        return (int)$result['count'] === 0;
    }

    /**
     * الحصول على الأوقات المتاحة للطبيب في يوم معين
     */
    public function getAvailableTimeSlots($doctorId, $date)
    {
        // أوقات العمل الافتراضية (يمكن تخصيصها لاحقاً)
        $workingHours = [
            '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
            '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00'
        ];

        // الحصول على المواعيد المحجوزة
        $bookedSlots = $this->db->select(
            "SELECT appointment_time FROM {$this->table} 
             WHERE doctor_id = :doctor_id 
             AND appointment_date = :date 
             AND status NOT IN ('cancelled', 'no_show')",
            [':doctor_id' => $doctorId, ':date' => $date]
        );

        $bookedTimes = array_column($bookedSlots, 'appointment_time');

        // إرجاع الأوقات المتاحة
        return array_diff($workingHours, $bookedTimes);
    }

    /**
     * تأكيد الموعد
     */
    public function confirm($id)
    {
        return $this->update($id, ['status' => 'confirmed']);
    }

    /**
     * إلغاء الموعد
     */
    public function cancel($id, $reason = null)
    {
        $data = ['status' => 'cancelled'];
        if ($reason) {
            $data['notes'] = $reason;
        }
        return $this->update($id, $data);
    }

    /**
     * تسجيل عدم الحضور
     */
    public function markNoShow($id)
    {
        return $this->update($id, ['status' => 'no_show']);
    }

    /**
     * إكمال الموعد
     */
    public function complete($id, $notes = null)
    {
        $data = ['status' => 'completed'];
        if ($notes) {
            $data['notes'] = $notes;
        }
        return $this->update($id, $data);
    }

    /**
     * البحث في المواعيد
     */
    public function search($term, $filters = [])
    {
        $query = "SELECT a.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name
                  FROM {$this->table} a
                  LEFT JOIN users patient ON a.patient_id = patient.id
                  LEFT JOIN users doctor ON a.doctor_id = doctor.id
                  WHERE (patient.first_name LIKE :term OR patient.last_name LIKE :term 
                         OR doctor.first_name LIKE :term OR doctor.last_name LIKE :term)";
        
        $params = [':term' => "%$term%"];

        // تطبيق المرشحات
        if (!empty($filters['status'])) {
            $query .= " AND a.status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($filters['doctor_id'])) {
            $query .= " AND a.doctor_id = :doctor_id";
            $params[':doctor_id'] = $filters['doctor_id'];
        }

        if (!empty($filters['patient_id'])) {
            $query .= " AND a.patient_id = :patient_id";
            $params[':patient_id'] = $filters['patient_id'];
        }

        if (!empty($filters['date_from'])) {
            $query .= " AND a.appointment_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $query .= " AND a.appointment_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        $query .= " ORDER BY a.appointment_date DESC, a.appointment_time DESC LIMIT 20";

        return $this->db->select($query, $params);
    }

    /**
     * إحصائيات المواعيد
     */
    public function getStats($doctorId = null)
    {
        $stats = [];
        $whereClause = $doctorId ? "WHERE doctor_id = :doctor_id" : "";
        $params = $doctorId ? [':doctor_id' => $doctorId] : [];

        // إجمالي المواعيد
        $total = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->table} $whereClause", $params);
        $stats['total'] = (int)$total['count'];

        // المواعيد حسب الحالة
        $byStatus = $this->db->select(
            "SELECT status, COUNT(*) as count FROM {$this->table} $whereClause GROUP BY status",
            $params
        );

        foreach ($byStatus as $status) {
            $stats[$status['status']] = (int)$status['count'];
        }

        // المواعيد اليوم
        $today = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE appointment_date = CURDATE() 
             " . ($doctorId ? "AND doctor_id = :doctor_id" : ""),
            $params
        );
        $stats['today'] = (int)$today['count'];

        // المواعيد هذا الأسبوع
        $thisWeek = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE WEEK(appointment_date) = WEEK(NOW()) AND YEAR(appointment_date) = YEAR(NOW()) 
             " . ($doctorId ? "AND doctor_id = :doctor_id" : ""),
            $params
        );
        $stats['this_week'] = (int)$thisWeek['count'];

        return $stats;
    }

    /**
     * الحصول على تقرير المواعيد
     */
    public function getReport($startDate, $endDate, $doctorId = null)
    {
        $query = "SELECT a.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name,
                         d.specialization as doctor_specialization
                  FROM {$this->table} a
                  LEFT JOIN users patient ON a.patient_id = patient.id
                  LEFT JOIN users doctor ON a.doctor_id = doctor.id
                  LEFT JOIN doctors d ON doctor.id = d.user_id
                  WHERE a.appointment_date BETWEEN :start_date AND :end_date";
        
        $params = [
            ':start_date' => $startDate,
            ':end_date' => $endDate
        ];

        if ($doctorId) {
            $query .= " AND a.doctor_id = :doctor_id";
            $params[':doctor_id'] = $doctorId;
        }

        $query .= " ORDER BY a.appointment_date ASC, a.appointment_time ASC";

        return $this->db->select($query, $params);
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validate($data)
    {
        $errors = [];

        // التحقق من الحقول المطلوبة
        $required = ['patient_id', 'doctor_id', 'appointment_date', 'appointment_time'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[$field] = "حقل $field مطلوب";
            }
        }

        // التحقق من التاريخ
        if (!empty($data['appointment_date'])) {
            if (strtotime($data['appointment_date']) < strtotime(date('Y-m-d'))) {
                $errors['appointment_date'] = 'لا يمكن حجز موعد في الماضي';
            }
        }

        // التحقق من الوقت
        if (!empty($data['appointment_time'])) {
            if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $data['appointment_time'])) {
                $errors['appointment_time'] = 'صيغة الوقت غير صحيحة';
            }
        }

        // التحقق من توفر الموعد
        if (!empty($data['doctor_id']) && !empty($data['appointment_date']) && !empty($data['appointment_time'])) {
            $excludeId = $data['id'] ?? null;
            if (!$this->isTimeSlotAvailable($data['doctor_id'], $data['appointment_date'], $data['appointment_time'], $excludeId)) {
                $errors['appointment_time'] = 'هذا الموعد محجوز بالفعل';
            }
        }

        return $errors;
    }

    /**
     * الحصول على حالة الموعد باللغة العربية
     */
    public static function getStatusLabel($status)
    {
        $statuses = [
            'scheduled' => 'مجدول',
            'confirmed' => 'مؤكد',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            'no_show' => 'لم يحضر'
        ];

        return $statuses[$status] ?? $status;
    }

    /**
     * الحصول على لون حالة الموعد
     */
    public static function getStatusColor($status)
    {
        $colors = [
            'scheduled' => 'warning',
            'confirmed' => 'info',
            'completed' => 'success',
            'cancelled' => 'danger',
            'no_show' => 'secondary'
        ];

        return $colors[$status] ?? 'secondary';
    }
}
