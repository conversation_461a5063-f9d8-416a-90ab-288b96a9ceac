<?php

/**
 * مساعد التاريخ والوقت
 * يوفر وظائف لتنسيق وإدارة التواريخ والأوقات باللغة العربية
 */
class DateHelper
{
    /**
     * أسماء الأشهر باللغة العربية
     */
    private static $arabicMonths = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];

    /**
     * أسماء الأشهر الهجرية
     */
    private static $hijriMonths = [
        1 => 'محرم', 2 => 'صفر', 3 => 'ربيع الأول', 4 => 'ربيع الثاني',
        5 => 'جمادى الأولى', 6 => 'جمادى الثانية', 7 => 'رجب', 8 => 'شعبان',
        9 => 'رمضان', 10 => 'شوال', 11 => 'ذو القعدة', 12 => 'ذو الحجة'
    ];

    /**
     * أسماء أيام الأسبوع باللغة العربية
     */
    private static $arabicDays = [
        'Sunday' => 'الأحد', 'Monday' => 'الاثنين', 'Tuesday' => 'الثلاثاء',
        'Wednesday' => 'الأربعاء', 'Thursday' => 'الخميس', 'Friday' => 'الجمعة',
        'Saturday' => 'السبت'
    ];

    /**
     * تنسيق التاريخ باللغة العربية
     */
    public static function formatArabic($date, $format = 'full')
    {
        if (empty($date)) {
            return '';
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        
        if (!$timestamp) {
            return $date;
        }

        $day = date('j', $timestamp);
        $month = (int)date('n', $timestamp);
        $year = date('Y', $timestamp);
        $dayName = self::$arabicDays[date('l', $timestamp)];
        $monthName = self::$arabicMonths[$month];

        switch ($format) {
            case 'full':
                return "$dayName، $day $monthName $year";
            case 'date':
                return "$day $monthName $year";
            case 'short':
                return "$day/$month/$year";
            case 'month_year':
                return "$monthName $year";
            case 'day_month':
                return "$day $monthName";
            default:
                return date($format, $timestamp);
        }
    }

    /**
     * تنسيق الوقت باللغة العربية
     */
    public static function formatTime($time, $format = '12')
    {
        if (empty($time)) {
            return '';
        }

        $timestamp = is_numeric($time) ? $time : strtotime($time);
        
        if (!$timestamp) {
            return $time;
        }

        if ($format === '12') {
            $hour = date('g', $timestamp);
            $minute = date('i', $timestamp);
            $ampm = date('A', $timestamp) === 'AM' ? 'ص' : 'م';
            return "$hour:$minute $ampm";
        } else {
            return date('H:i', $timestamp);
        }
    }

    /**
     * تنسيق التاريخ والوقت معاً
     */
    public static function formatDateTime($datetime, $dateFormat = 'date', $timeFormat = '12')
    {
        if (empty($datetime)) {
            return '';
        }

        $date = self::formatArabic($datetime, $dateFormat);
        $time = self::formatTime($datetime, $timeFormat);
        
        return "$date - $time";
    }

    /**
     * حساب الفرق بين تاريخين
     */
    public static function diffForHumans($date, $now = null)
    {
        if (empty($date)) {
            return '';
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        $now = $now ? (is_numeric($now) ? $now : strtotime($now)) : time();
        
        if (!$timestamp) {
            return $date;
        }

        $diff = $now - $timestamp;
        $absDiff = abs($diff);
        $future = $diff < 0;

        if ($absDiff < 60) {
            return 'الآن';
        } elseif ($absDiff < 3600) {
            $minutes = floor($absDiff / 60);
            $text = $minutes === 1 ? 'دقيقة' : "$minutes دقائق";
            return $future ? "خلال $text" : "منذ $text";
        } elseif ($absDiff < 86400) {
            $hours = floor($absDiff / 3600);
            $text = $hours === 1 ? 'ساعة' : "$hours ساعات";
            return $future ? "خلال $text" : "منذ $text";
        } elseif ($absDiff < 2592000) {
            $days = floor($absDiff / 86400);
            $text = $days === 1 ? 'يوم' : "$days أيام";
            return $future ? "خلال $text" : "منذ $text";
        } elseif ($absDiff < 31536000) {
            $months = floor($absDiff / 2592000);
            $text = $months === 1 ? 'شهر' : "$months أشهر";
            return $future ? "خلال $text" : "منذ $text";
        } else {
            $years = floor($absDiff / 31536000);
            $text = $years === 1 ? 'سنة' : "$years سنوات";
            return $future ? "خلال $text" : "منذ $text";
        }
    }

    /**
     * حساب العمر
     */
    public static function calculateAge($birthDate, $currentDate = null)
    {
        if (empty($birthDate)) {
            return 0;
        }

        $birth = new DateTime($birthDate);
        $current = $currentDate ? new DateTime($currentDate) : new DateTime();
        
        return $birth->diff($current)->y;
    }

    /**
     * تحويل التاريخ الميلادي إلى هجري (تقريبي)
     */
    public static function toHijri($date)
    {
        if (empty($date)) {
            return '';
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        
        if (!$timestamp) {
            return $date;
        }

        // تحويل تقريبي (يحتاج لمكتبة متخصصة للدقة)
        $gregorianYear = date('Y', $timestamp);
        $hijriYear = floor(($gregorianYear - 622) * 1.030684);
        
        return "حوالي $hijriYear هـ";
    }

    /**
     * التحقق من كون التاريخ في الماضي
     */
    public static function isPast($date)
    {
        if (empty($date)) {
            return false;
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return $timestamp < time();
    }

    /**
     * التحقق من كون التاريخ في المستقبل
     */
    public static function isFuture($date)
    {
        if (empty($date)) {
            return false;
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return $timestamp > time();
    }

    /**
     * التحقق من كون التاريخ اليوم
     */
    public static function isToday($date)
    {
        if (empty($date)) {
            return false;
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return date('Y-m-d', $timestamp) === date('Y-m-d');
    }

    /**
     * التحقق من كون التاريخ أمس
     */
    public static function isYesterday($date)
    {
        if (empty($date)) {
            return false;
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return date('Y-m-d', $timestamp) === date('Y-m-d', strtotime('-1 day'));
    }

    /**
     * التحقق من كون التاريخ غداً
     */
    public static function isTomorrow($date)
    {
        if (empty($date)) {
            return false;
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return date('Y-m-d', $timestamp) === date('Y-m-d', strtotime('+1 day'));
    }

    /**
     * الحصول على بداية اليوم
     */
    public static function startOfDay($date = null)
    {
        $date = $date ?? date('Y-m-d');
        return date('Y-m-d 00:00:00', strtotime($date));
    }

    /**
     * الحصول على نهاية اليوم
     */
    public static function endOfDay($date = null)
    {
        $date = $date ?? date('Y-m-d');
        return date('Y-m-d 23:59:59', strtotime($date));
    }

    /**
     * الحصول على بداية الأسبوع
     */
    public static function startOfWeek($date = null)
    {
        $timestamp = $date ? strtotime($date) : time();
        $dayOfWeek = date('w', $timestamp);
        $startOfWeek = $timestamp - ($dayOfWeek * 86400);
        return date('Y-m-d', $startOfWeek);
    }

    /**
     * الحصول على نهاية الأسبوع
     */
    public static function endOfWeek($date = null)
    {
        $timestamp = $date ? strtotime($date) : time();
        $dayOfWeek = date('w', $timestamp);
        $endOfWeek = $timestamp + ((6 - $dayOfWeek) * 86400);
        return date('Y-m-d', $endOfWeek);
    }

    /**
     * الحصول على بداية الشهر
     */
    public static function startOfMonth($date = null)
    {
        $date = $date ?? date('Y-m-d');
        return date('Y-m-01', strtotime($date));
    }

    /**
     * الحصول على نهاية الشهر
     */
    public static function endOfMonth($date = null)
    {
        $date = $date ?? date('Y-m-d');
        return date('Y-m-t', strtotime($date));
    }

    /**
     * إضافة أيام لتاريخ
     */
    public static function addDays($date, $days)
    {
        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return date('Y-m-d', $timestamp + ($days * 86400));
    }

    /**
     * طرح أيام من تاريخ
     */
    public static function subDays($date, $days)
    {
        return self::addDays($date, -$days);
    }

    /**
     * إضافة أشهر لتاريخ
     */
    public static function addMonths($date, $months)
    {
        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return date('Y-m-d', strtotime("+$months months", $timestamp));
    }

    /**
     * طرح أشهر من تاريخ
     */
    public static function subMonths($date, $months)
    {
        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return date('Y-m-d', strtotime("-$months months", $timestamp));
    }

    /**
     * الحصول على قائمة بالتواريخ بين تاريخين
     */
    public static function getDateRange($startDate, $endDate, $format = 'Y-m-d')
    {
        $dates = [];
        $current = strtotime($startDate);
        $end = strtotime($endDate);

        while ($current <= $end) {
            $dates[] = date($format, $current);
            $current = strtotime('+1 day', $current);
        }

        return $dates;
    }

    /**
     * تنسيق التاريخ للقاعدة البيانات
     */
    public static function toDatabase($date)
    {
        if (empty($date)) {
            return null;
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return $timestamp ? date('Y-m-d H:i:s', $timestamp) : null;
    }

    /**
     * تنسيق التاريخ من قاعدة البيانات للعرض
     */
    public static function fromDatabase($date, $format = 'full')
    {
        if (empty($date)) {
            return '';
        }

        return self::formatArabic($date, $format);
    }

    /**
     * الحصول على التاريخ الحالي بتنسيق معين
     */
    public static function now($format = 'Y-m-d H:i:s')
    {
        return date($format);
    }

    /**
     * الحصول على التاريخ الحالي باللغة العربية
     */
    public static function nowArabic($format = 'full')
    {
        return self::formatArabic(time(), $format);
    }

    /**
     * التحقق من صحة التاريخ
     */
    public static function isValid($date, $format = 'Y-m-d')
    {
        $dateTime = DateTime::createFromFormat($format, $date);
        return $dateTime && $dateTime->format($format) === $date;
    }

    /**
     * مقارنة تاريخين
     */
    public static function compare($date1, $date2)
    {
        $timestamp1 = is_numeric($date1) ? $date1 : strtotime($date1);
        $timestamp2 = is_numeric($date2) ? $date2 : strtotime($date2);

        if ($timestamp1 < $timestamp2) {
            return -1;
        } elseif ($timestamp1 > $timestamp2) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * الحصول على أوقات الصلاة (تقريبية)
     */
    public static function getPrayerTimes($date = null, $city = 'Riyadh')
    {
        // هذه أوقات تقريبية لمدينة الرياض
        // في التطبيق الحقيقي، يجب استخدام API متخصص
        return [
            'fajr' => '05:30',
            'sunrise' => '06:45',
            'dhuhr' => '12:15',
            'asr' => '15:30',
            'maghrib' => '18:00',
            'isha' => '19:30'
        ];
    }

    /**
     * تحويل الوقت إلى منطقة زمنية مختلفة
     */
    public static function convertTimezone($datetime, $fromTz = 'UTC', $toTz = 'Asia/Riyadh')
    {
        try {
            $date = new DateTime($datetime, new DateTimeZone($fromTz));
            $date->setTimezone(new DateTimeZone($toTz));
            return $date->format('Y-m-d H:i:s');
        } catch (Exception $e) {
            return $datetime;
        }
    }

    /**
     * الحصول على الفصل الحالي
     */
    public static function getCurrentSeason($date = null)
    {
        $month = $date ? date('n', strtotime($date)) : date('n');
        
        if (in_array($month, [12, 1, 2])) {
            return 'الشتاء';
        } elseif (in_array($month, [3, 4, 5])) {
            return 'الربيع';
        } elseif (in_array($month, [6, 7, 8])) {
            return 'الصيف';
        } else {
            return 'الخريف';
        }
    }
}
