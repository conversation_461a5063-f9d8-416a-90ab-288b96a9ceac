<?php

/**
 * مساعد إدارة الملفات
 * يوفر وظائف لرفع وإدارة ومعالجة الملفات
 */
class FileHelper
{
    /**
     * أنواع الملفات المسموحة
     */
    private static $allowedTypes = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'document' => ['pdf', 'doc', 'docx', 'txt', 'rtf'],
        'spreadsheet' => ['xls', 'xlsx', 'csv'],
        'archive' => ['zip', 'rar', '7z'],
        'audio' => ['mp3', 'wav', 'ogg'],
        'video' => ['mp4', 'avi', 'mov', 'wmv']
    ];

    /**
     * الحد الأقصى لحجم الملف (بالبايت)
     */
    private static $maxFileSize = 10485760; // 10 MB

    /**
     * مجلد الرفع الافتراضي
     */
    private static $uploadPath = 'uploads/';

    /**
     * رفع ملف
     */
    public static function upload($file, $options = [])
    {
        // التحقق من وجود الملف
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return ['success' => false, 'message' => 'لم يتم رفع أي ملف'];
        }

        // التحقق من الأخطاء
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => self::getUploadErrorMessage($file['error'])];
        }

        // إعدادات الرفع
        $allowedTypes = $options['allowed_types'] ?? self::$allowedTypes['image'];
        $maxSize = $options['max_size'] ?? self::$maxFileSize;
        $uploadPath = $options['upload_path'] ?? self::$uploadPath;
        $customName = $options['custom_name'] ?? null;

        // التحقق من حجم الملف
        if ($file['size'] > $maxSize) {
            $maxSizeMB = round($maxSize / 1024 / 1024, 2);
            return ['success' => false, 'message' => "حجم الملف يجب أن يكون أقل من {$maxSizeMB} ميجابايت"];
        }

        // التحقق من نوع الملف
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            $allowedTypesStr = implode(', ', $allowedTypes);
            return ['success' => false, 'message' => "أنواع الملفات المسموحة: $allowedTypesStr"];
        }

        // التحقق من MIME type للأمان
        if (!self::isValidMimeType($file['tmp_name'], $extension)) {
            return ['success' => false, 'message' => 'نوع الملف غير صحيح'];
        }

        // إنشاء مجلد الرفع إذا لم يكن موجوداً
        $fullUploadPath = self::createUploadDirectory($uploadPath);
        if (!$fullUploadPath) {
            return ['success' => false, 'message' => 'فشل في إنشاء مجلد الرفع'];
        }

        // إنشاء اسم ملف فريد
        $fileName = $customName ? $customName . '.' . $extension : self::generateUniqueFileName($extension);
        $filePath = $fullUploadPath . $fileName;

        // رفع الملف
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            // تحسين الصورة إذا كانت صورة
            if (in_array($extension, self::$allowedTypes['image'])) {
                self::optimizeImage($filePath, $options);
            }

            return [
                'success' => true,
                'filename' => $fileName,
                'path' => $filePath,
                'url' => self::getFileUrl($uploadPath . $fileName),
                'size' => $file['size'],
                'type' => $extension
            ];
        } else {
            return ['success' => false, 'message' => 'فشل في رفع الملف'];
        }
    }

    /**
     * رفع ملفات متعددة
     */
    public static function uploadMultiple($files, $options = [])
    {
        $results = [];
        
        // تحويل تنسيق $_FILES للملفات المتعددة
        $fileArray = self::reArrayFiles($files);
        
        foreach ($fileArray as $file) {
            $results[] = self::upload($file, $options);
        }
        
        return $results;
    }

    /**
     * حذف ملف
     */
    public static function delete($filePath)
    {
        if (file_exists($filePath) && is_file($filePath)) {
            return unlink($filePath);
        }
        return false;
    }

    /**
     * نسخ ملف
     */
    public static function copy($source, $destination)
    {
        if (!file_exists($source)) {
            return false;
        }

        $destinationDir = dirname($destination);
        if (!is_dir($destinationDir)) {
            mkdir($destinationDir, 0755, true);
        }

        return copy($source, $destination);
    }

    /**
     * نقل ملف
     */
    public static function move($source, $destination)
    {
        if (self::copy($source, $destination)) {
            return self::delete($source);
        }
        return false;
    }

    /**
     * الحصول على معلومات الملف
     */
    public static function getFileInfo($filePath)
    {
        if (!file_exists($filePath)) {
            return null;
        }

        $pathInfo = pathinfo($filePath);
        
        return [
            'name' => $pathInfo['basename'],
            'extension' => $pathInfo['extension'] ?? '',
            'size' => filesize($filePath),
            'size_formatted' => self::formatFileSize(filesize($filePath)),
            'modified' => filemtime($filePath),
            'type' => self::getFileType($pathInfo['extension'] ?? ''),
            'mime_type' => mime_content_type($filePath),
            'is_image' => self::isImage($filePath)
        ];
    }

    /**
     * تنسيق حجم الملف
     */
    public static function formatFileSize($bytes)
    {
        $units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * التحقق من كون الملف صورة
     */
    public static function isImage($filePath)
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        return in_array($extension, self::$allowedTypes['image']);
    }

    /**
     * إنشاء صورة مصغرة
     */
    public static function createThumbnail($imagePath, $width = 150, $height = 150, $quality = 80)
    {
        if (!self::isImage($imagePath) || !file_exists($imagePath)) {
            return false;
        }

        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $pathInfo['dirname'] . '/thumb_' . $pathInfo['basename'];

        // الحصول على أبعاد الصورة الأصلية
        list($originalWidth, $originalHeight, $imageType) = getimagesize($imagePath);

        // حساب الأبعاد الجديدة مع الحفاظ على النسبة
        $ratio = min($width / $originalWidth, $height / $originalHeight);
        $newWidth = $originalWidth * $ratio;
        $newHeight = $originalHeight * $ratio;

        // إنشاء الصورة الجديدة
        $thumbnail = imagecreatetruecolor($newWidth, $newHeight);

        // إنشاء الصورة الأصلية حسب النوع
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($imagePath);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($imagePath);
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($imagePath);
                break;
            default:
                return false;
        }

        // تغيير حجم الصورة
        imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

        // حفظ الصورة المصغرة
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($thumbnail, $thumbnailPath, $quality);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($thumbnail, $thumbnailPath);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($thumbnail, $thumbnailPath);
                break;
        }

        // تنظيف الذاكرة
        imagedestroy($source);
        imagedestroy($thumbnail);

        return $result ? $thumbnailPath : false;
    }

    /**
     * ضغط الصورة
     */
    public static function compressImage($imagePath, $quality = 80)
    {
        if (!self::isImage($imagePath) || !file_exists($imagePath)) {
            return false;
        }

        list($width, $height, $imageType) = getimagesize($imagePath);

        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($imagePath);
                return imagejpeg($source, $imagePath, $quality);
                
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($imagePath);
                return imagepng($source, $imagePath, 9);
                
            default:
                return false;
        }
    }

    /**
     * تحويل صيغة الصورة
     */
    public static function convertImage($imagePath, $newFormat, $quality = 80)
    {
        if (!self::isImage($imagePath) || !file_exists($imagePath)) {
            return false;
        }

        $pathInfo = pathinfo($imagePath);
        $newPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.' . $newFormat;

        list($width, $height, $imageType) = getimagesize($imagePath);

        // إنشاء الصورة الأصلية
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($imagePath);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($imagePath);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($imagePath);
                break;
            default:
                return false;
        }

        // حفظ بالصيغة الجديدة
        switch (strtolower($newFormat)) {
            case 'jpg':
            case 'jpeg':
                $result = imagejpeg($source, $newPath, $quality);
                break;
            case 'png':
                $result = imagepng($source, $newPath);
                break;
            case 'gif':
                $result = imagegif($source, $newPath);
                break;
            default:
                return false;
        }

        imagedestroy($source);
        return $result ? $newPath : false;
    }

    /**
     * قراءة ملف CSV
     */
    public static function readCSV($filePath, $delimiter = ',', $hasHeader = true)
    {
        if (!file_exists($filePath)) {
            return false;
        }

        $data = [];
        $headers = [];
        
        if (($handle = fopen($filePath, 'r')) !== false) {
            $rowIndex = 0;
            
            while (($row = fgetcsv($handle, 1000, $delimiter)) !== false) {
                if ($hasHeader && $rowIndex === 0) {
                    $headers = $row;
                } else {
                    if ($hasHeader && !empty($headers)) {
                        $data[] = array_combine($headers, $row);
                    } else {
                        $data[] = $row;
                    }
                }
                $rowIndex++;
            }
            
            fclose($handle);
        }
        
        return $data;
    }

    /**
     * كتابة ملف CSV
     */
    public static function writeCSV($filePath, $data, $headers = null)
    {
        if (($handle = fopen($filePath, 'w')) !== false) {
            // كتابة العناوين
            if ($headers) {
                fputcsv($handle, $headers);
            }
            
            // كتابة البيانات
            foreach ($data as $row) {
                fputcsv($handle, $row);
            }
            
            fclose($handle);
            return true;
        }
        
        return false;
    }

    /**
     * إنشاء مجلد الرفع
     */
    private static function createUploadDirectory($path)
    {
        $fullPath = rtrim($path, '/') . '/';
        
        if (!is_dir($fullPath)) {
            if (!mkdir($fullPath, 0755, true)) {
                return false;
            }
        }
        
        return $fullPath;
    }

    /**
     * إنشاء اسم ملف فريد
     */
    private static function generateUniqueFileName($extension)
    {
        return uniqid() . '_' . time() . '.' . $extension;
    }

    /**
     * التحقق من MIME type
     */
    private static function isValidMimeType($filePath, $extension)
    {
        $validMimes = [
            'jpg' => ['image/jpeg', 'image/jpg'],
            'jpeg' => ['image/jpeg', 'image/jpg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'pdf' => ['application/pdf'],
            'doc' => ['application/msword'],
            'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'txt' => ['text/plain'],
            'csv' => ['text/csv', 'application/csv']
        ];

        if (!isset($validMimes[$extension])) {
            return true; // إذا لم يكن النوع محدد، نفترض أنه صحيح
        }

        $fileMime = mime_content_type($filePath);
        return in_array($fileMime, $validMimes[$extension]);
    }

    /**
     * الحصول على رسالة خطأ الرفع
     */
    private static function getUploadErrorMessage($errorCode)
    {
        $errors = [
            UPLOAD_ERR_INI_SIZE => 'حجم الملف أكبر من الحد المسموح',
            UPLOAD_ERR_FORM_SIZE => 'حجم الملف أكبر من الحد المحدد في النموذج',
            UPLOAD_ERR_PARTIAL => 'تم رفع جزء من الملف فقط',
            UPLOAD_ERR_NO_FILE => 'لم يتم رفع أي ملف',
            UPLOAD_ERR_NO_TMP_DIR => 'مجلد الملفات المؤقتة غير موجود',
            UPLOAD_ERR_CANT_WRITE => 'فشل في كتابة الملف',
            UPLOAD_ERR_EXTENSION => 'امتداد PHP أوقف رفع الملف'
        ];

        return $errors[$errorCode] ?? 'خطأ غير معروف في رفع الملف';
    }

    /**
     * إعادة ترتيب مصفوفة الملفات المتعددة
     */
    private static function reArrayFiles($filePost)
    {
        $fileArray = [];
        $fileCount = count($filePost['name']);
        $fileKeys = array_keys($filePost);

        for ($i = 0; $i < $fileCount; $i++) {
            foreach ($fileKeys as $key) {
                $fileArray[$i][$key] = $filePost[$key][$i];
            }
        }

        return $fileArray;
    }

    /**
     * تحسين الصورة
     */
    private static function optimizeImage($imagePath, $options)
    {
        $maxWidth = $options['max_width'] ?? 1920;
        $maxHeight = $options['max_height'] ?? 1080;
        $quality = $options['quality'] ?? 80;

        list($width, $height, $imageType) = getimagesize($imagePath);

        // تغيير الحجم إذا كان أكبر من الحد المسموح
        if ($width > $maxWidth || $height > $maxHeight) {
            $ratio = min($maxWidth / $width, $maxHeight / $height);
            $newWidth = $width * $ratio;
            $newHeight = $height * $ratio;

            $resized = imagecreatetruecolor($newWidth, $newHeight);

            switch ($imageType) {
                case IMAGETYPE_JPEG:
                    $source = imagecreatefromjpeg($imagePath);
                    imagecopyresampled($resized, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                    imagejpeg($resized, $imagePath, $quality);
                    break;
                case IMAGETYPE_PNG:
                    $source = imagecreatefrompng($imagePath);
                    imagealphablending($resized, false);
                    imagesavealpha($resized, true);
                    imagecopyresampled($resized, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                    imagepng($resized, $imagePath);
                    break;
            }

            imagedestroy($source);
            imagedestroy($resized);
        } else {
            // ضغط الصورة فقط
            self::compressImage($imagePath, $quality);
        }
    }

    /**
     * الحصول على نوع الملف
     */
    private static function getFileType($extension)
    {
        foreach (self::$allowedTypes as $type => $extensions) {
            if (in_array(strtolower($extension), $extensions)) {
                return $type;
            }
        }
        return 'unknown';
    }

    /**
     * الحصول على رابط الملف
     */
    private static function getFileUrl($relativePath)
    {
        return rtrim(APP_URL, '/') . '/' . ltrim($relativePath, '/');
    }
}
