<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' . APP_NAME : APP_NAME ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= App::url('public/css/style.css') ?>" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 90%;
            min-height: 500px;
        }
        
        .auth-header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .auth-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #2c5aa0;
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: transform 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, #1e3f73 0%, #2c5aa0 100%);
        }
        
        .btn-outline-primary {
            border: 2px solid #2c5aa0;
            color: #2c5aa0;
            border-radius: 8px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background-color: #2c5aa0;
            border-color: #2c5aa0;
            transform: translateY(-2px);
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
        }
        
        .auth-links {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .auth-links a {
            color: #2c5aa0;
            text-decoration: none;
            font-weight: 500;
        }
        
        .auth-links a:hover {
            text-decoration: underline;
        }
        
        .password-toggle {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
        }
        
        .password-field {
            position: relative;
        }
        
        .user-type-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .user-type-card {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .user-type-card:hover {
            border-color: #2c5aa0;
            background-color: rgba(44, 90, 160, 0.05);
        }
        
        .user-type-card.selected {
            border-color: #2c5aa0;
            background-color: rgba(44, 90, 160, 0.1);
        }
        
        .user-type-card i {
            font-size: 2rem;
            color: #2c5aa0;
            margin-bottom: 0.5rem;
        }
        
        .user-type-card h6 {
            margin: 0;
            font-weight: 600;
        }
        
        .user-type-card p {
            margin: 0;
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            .auth-container {
                margin: 1rem;
                width: calc(100% - 2rem);
            }
            
            .auth-header, .auth-body {
                padding: 1.5rem;
            }
            
            .user-type-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h1>
                <i class="bi bi-heart-pulse-fill me-2"></i>
                <?= APP_NAME ?>
            </h1>
            <p>نظام إدارة السجلات الطبية الإلكترونية</p>
        </div>
        
        <div class="auth-body">
            <!-- Flash Messages -->
            <?php
            $flashMessage = null;
            if (isset($_SESSION['flash_message'])) {
                $flashMessage = [
                    'text' => $_SESSION['flash_message'],
                    'type' => $_SESSION['flash_type'] ?? 'info'
                ];
                unset($_SESSION['flash_message']);
                unset($_SESSION['flash_type']);
            }
            
            if ($flashMessage):
                $alertClass = match($flashMessage['type']) {
                    'error' => 'alert-danger',
                    'success' => 'alert-success',
                    'warning' => 'alert-warning',
                    default => 'alert-info'
                };
            ?>
                <div class="alert <?= $alertClass ?> alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($flashMessage['text']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Page Content -->
            <?= $content ?? '' ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="<?= App::url('public/js/main.js') ?>"></script>
    
    <script>
        // تفعيل/إلغاء تفعيل كلمة المرور
        $(document).ready(function() {
            $('.password-toggle').on('click', function() {
                const input = $(this).siblings('input');
                const icon = $(this).find('i');
                
                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                    icon.removeClass('bi-eye').addClass('bi-eye-slash');
                } else {
                    input.attr('type', 'password');
                    icon.removeClass('bi-eye-slash').addClass('bi-eye');
                }
            });
            
            // اختيار نوع المستخدم
            $('.user-type-card').on('click', function() {
                $('.user-type-card').removeClass('selected');
                $(this).addClass('selected');
                
                const userType = $(this).data('type');
                $('input[name="user_type"]').val(userType);
                
                // إظهار/إخفاء الحقول الإضافية
                $('.additional-fields').hide();
                $(`.${userType}-fields`).show();
            });
            
            // التحقق من النموذج
            $('form').on('submit', function(e) {
                let isValid = true;
                
                // التحقق من الحقول المطلوبة
                $(this).find('[required]').each(function() {
                    if (!this.value.trim()) {
                        $(this).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                    }
                });
                
                // التحقق من تطابق كلمات المرور
                const password = $('input[name="password"]').val();
                const confirmPassword = $('input[name="confirm_password"]').val();
                
                if (password && confirmPassword && password !== confirmPassword) {
                    $('input[name="confirm_password"]').addClass('is-invalid');
                    isValid = false;
                }
                
                if (!isValid) {
                    e.preventDefault();
                    showAlert('يرجى تصحيح الأخطاء في النموذج', 'error');
                }
            });
        });
    </script>
</body>
</html>
