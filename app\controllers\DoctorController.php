<?php

/**
 * متحكم الطبيب
 * يعالج جميع العمليات المتعلقة بالأطباء
 */
class DoctorController extends Controller
{
    private $userModel;
    private $appointmentModel;
    private $prescriptionModel;
    private $medicalRecordModel;
    private $notificationModel;

    public function __construct()
    {
        parent::__construct();
        
        // التحقق من تسجيل الدخول ونوع المستخدم
        $this->requireAuth();
        $this->requireUserType('doctor');
        
        // تحميل النماذج
        $this->userModel = new User();
        $this->appointmentModel = new Appointment();
        $this->prescriptionModel = new Prescription();
        $this->medicalRecordModel = new MedicalRecord();
        $this->notificationModel = new Notification();
    }

    /**
     * لوحة تحكم الطبيب
     */
    public function dashboard()
    {
        $doctorId = $this->currentUser['id'];
        
        // الحصول على البيانات للوحة التحكم
        $data = [
            'title' => 'لوحة تحكم الطبيب',
            'doctor' => $this->currentUser,
            'todayAppointments' => $this->appointmentModel->getToday($doctorId),
            'upcomingAppointments' => $this->appointmentModel->getUpcoming($doctorId, 'doctor', 5),
            'recentPrescriptions' => $this->prescriptionModel->getByDoctor($doctorId, 5),
            'recentMedicalRecords' => $this->medicalRecordModel->getByDoctor($doctorId, 5),
            'notifications' => $this->notificationModel->getByUser($doctorId, false, 5),
            'unreadNotifications' => $this->notificationModel->getUnreadCount($doctorId),
            'stats' => [
                'appointments' => $this->appointmentModel->getStats($doctorId),
                'prescriptions' => $this->prescriptionModel->getStats($doctorId),
                'medical_records' => $this->medicalRecordModel->getStats($doctorId)
            ]
        ];

        $this->view('doctor/dashboard', $data);
    }

    /**
     * المواعيد
     */
    public function appointments()
    {
        $doctorId = $this->currentUser['id'];
        $date = App::get('date', date('Y-m-d'));
        $status = App::get('status');
        
        $data = [
            'title' => 'المواعيد',
            'appointments' => $this->appointmentModel->getByDoctor($doctorId, $date, $status),
            'currentDate' => $date,
            'currentStatus' => $status,
            'todayCount' => count($this->appointmentModel->getToday($doctorId))
        ];

        $this->view('doctor/appointments', $data);
    }

    /**
     * تأكيد الموعد
     */
    public function confirmAppointment()
    {
        $appointmentId = App::post('appointment_id');
        
        if (!$appointmentId) {
            $this->json(['success' => false, 'message' => 'معرف الموعد مطلوب'], 400);
            return;
        }

        // التحقق من ملكية الموعد
        $appointment = $this->appointmentModel->findById($appointmentId);
        if (!$appointment || $appointment['doctor_id'] != $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'الموعد غير موجود'], 404);
            return;
        }

        if ($this->appointmentModel->confirm($appointmentId)) {
            $this->json(['success' => true, 'message' => 'تم تأكيد الموعد بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تأكيد الموعد'], 500);
        }
    }

    /**
     * إكمال الموعد
     */
    public function completeAppointment()
    {
        $appointmentId = App::post('appointment_id');
        $notes = App::post('notes');
        
        if (!$appointmentId) {
            $this->json(['success' => false, 'message' => 'معرف الموعد مطلوب'], 400);
            return;
        }

        // التحقق من ملكية الموعد
        $appointment = $this->appointmentModel->findById($appointmentId);
        if (!$appointment || $appointment['doctor_id'] != $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'الموعد غير موجود'], 404);
            return;
        }

        if ($this->appointmentModel->complete($appointmentId, $notes)) {
            $this->json(['success' => true, 'message' => 'تم إكمال الموعد بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في إكمال الموعد'], 500);
        }
    }

    /**
     * المرضى
     */
    public function patients()
    {
        $doctorId = $this->currentUser['id'];
        $search = App::get('search', '');
        
        $patients = $this->userModel->getPatientsByDoctor($doctorId);
        
        // تطبيق البحث إذا تم تحديده
        if (!empty($search)) {
            $patients = array_filter($patients, function($patient) use ($search) {
                return stripos($patient['first_name'] . ' ' . $patient['last_name'], $search) !== false ||
                       stripos($patient['email'], $search) !== false ||
                       stripos($patient['phone'], $search) !== false;
            });
        }

        $data = [
            'title' => 'المرضى',
            'patients' => $patients,
            'search' => $search
        ];

        $this->view('doctor/patients', $data);
    }

    /**
     * عرض ملف المريض
     */
    public function viewPatient($patientId)
    {
        // التحقق من وجود المريض
        $patient = $this->userModel->findById($patientId);
        if (!$patient || $patient['user_type'] !== 'patient') {
            $this->setFlashMessage('المريض غير موجود', 'error');
            $this->redirect('doctor/patients');
            return;
        }

        $data = [
            'title' => 'ملف المريض - ' . User::getFullName($patient),
            'patient' => $patient,
            'medicalRecords' => $this->medicalRecordModel->getByPatient($patientId),
            'prescriptions' => $this->prescriptionModel->getByPatient($patientId),
            'appointments' => $this->appointmentModel->getByPatient($patientId),
            'allergies' => $this->medicalRecordModel->getAllergies($patientId),
            'labTests' => $this->medicalRecordModel->getLabTests($patientId),
            'summary' => $this->medicalRecordModel->getPatientSummary($patientId)
        ];

        $this->view('doctor/patient_profile', $data);
    }

    /**
     * إضافة سجل طبي
     */
    public function addMedicalRecord()
    {
        if (App::isPost()) {
            $this->processAddMedicalRecord();
            return;
        }

        $patientId = App::get('patient_id');
        if (!$patientId) {
            $this->setFlashMessage('معرف المريض مطلوب', 'error');
            $this->redirect('doctor/patients');
            return;
        }

        $patient = $this->userModel->findById($patientId);
        if (!$patient) {
            $this->setFlashMessage('المريض غير موجود', 'error');
            $this->redirect('doctor/patients');
            return;
        }

        $data = [
            'title' => 'إضافة سجل طبي',
            'patient' => $patient
        ];

        $this->view('doctor/add_medical_record', $data);
    }

    /**
     * معالجة إضافة السجل الطبي
     */
    private function processAddMedicalRecord()
    {
        $doctorId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->medicalRecordModel->validate(array_merge($postData, ['doctor_id' => $doctorId]));

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('doctor/add-medical-record?patient_id=' . $postData['patient_id']);
            return;
        }

        // إعداد البيانات
        $recordData = [
            'patient_id' => $postData['patient_id'],
            'doctor_id' => $doctorId,
            'visit_date' => $postData['visit_date'] ?? date('Y-m-d'),
            'chief_complaint' => $postData['chief_complaint'],
            'diagnosis' => $postData['diagnosis'],
            'treatment_plan' => $postData['treatment_plan'],
            'notes' => $postData['notes'] ?? null
        ];

        // إضافة العلامات الحيوية إذا تم إدخالها
        if (!empty($postData['vital_signs'])) {
            $recordData['vital_signs'] = $postData['vital_signs'];
        }

        $recordId = $this->medicalRecordModel->create($recordData);

        if ($recordId) {
            $this->setFlashMessage('تم إضافة السجل الطبي بنجاح', 'success');
            $this->redirect('doctor/patient/' . $postData['patient_id']);
        } else {
            $this->setFlashMessage('فشل في إضافة السجل الطبي', 'error');
            $this->redirect('doctor/add-medical-record?patient_id=' . $postData['patient_id']);
        }
    }

    /**
     * الوصفات الطبية
     */
    public function prescriptions()
    {
        $doctorId = $this->currentUser['id'];
        $status = App::get('status');
        
        $data = [
            'title' => 'الوصفات الطبية',
            'prescriptions' => $this->prescriptionModel->getByDoctor($doctorId),
            'currentStatus' => $status
        ];

        $this->view('doctor/prescriptions', $data);
    }

    /**
     * إنشاء وصفة طبية
     */
    public function createPrescription()
    {
        if (App::isPost()) {
            $this->processCreatePrescription();
            return;
        }

        $patientId = App::get('patient_id');
        if (!$patientId) {
            $this->setFlashMessage('معرف المريض مطلوب', 'error');
            $this->redirect('doctor/patients');
            return;
        }

        $patient = $this->userModel->findById($patientId);
        if (!$patient) {
            $this->setFlashMessage('المريض غير موجود', 'error');
            $this->redirect('doctor/patients');
            return;
        }

        $data = [
            'title' => 'إنشاء وصفة طبية',
            'patient' => $patient,
            'allergies' => $this->medicalRecordModel->getAllergies($patientId)
        ];

        $this->view('doctor/create_prescription', $data);
    }

    /**
     * معالجة إنشاء الوصفة الطبية
     */
    private function processCreatePrescription()
    {
        $doctorId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->prescriptionModel->validate(array_merge($postData, ['doctor_id' => $doctorId]));

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('doctor/create-prescription?patient_id=' . $postData['patient_id']);
            return;
        }

        // التحقق من التفاعلات الدوائية
        $interactions = $this->prescriptionModel->checkDrugInteractions($postData['medications']);
        if (!empty($interactions)) {
            SessionHelper::setFlash('drug_interactions', $interactions);
        }

        // إعداد البيانات
        $prescriptionData = [
            'patient_id' => $postData['patient_id'],
            'doctor_id' => $doctorId,
            'diagnosis' => $postData['diagnosis'],
            'notes' => $postData['notes'] ?? null,
            'medications' => $postData['medications'],
            'expiry_date' => $postData['expiry_date'] ?? date('Y-m-d', strtotime('+30 days'))
        ];

        $prescriptionId = $this->prescriptionModel->create($prescriptionData);

        if ($prescriptionId) {
            // إرسال إشعار للمريض
            $prescription = $this->prescriptionModel->findById($prescriptionId);
            $this->notificationModel->notifyNewPrescription(
                $postData['patient_id'],
                $this->currentUser['first_name'] . ' ' . $this->currentUser['last_name'],
                $prescription['prescription_code']
            );

            $this->setFlashMessage('تم إنشاء الوصفة الطبية بنجاح', 'success');
            $this->redirect('doctor/prescription/' . $prescriptionId);
        } else {
            $this->setFlashMessage('فشل في إنشاء الوصفة الطبية', 'error');
            $this->redirect('doctor/create-prescription?patient_id=' . $postData['patient_id']);
        }
    }

    /**
     * عرض تفاصيل الوصفة
     */
    public function viewPrescription($prescriptionId)
    {
        // التحقق من ملكية الوصفة
        $prescription = $this->prescriptionModel->findById($prescriptionId);
        if (!$prescription || $prescription['doctor_id'] != $this->currentUser['id']) {
            $this->setFlashMessage('الوصفة غير موجودة', 'error');
            $this->redirect('doctor/prescriptions');
            return;
        }

        $data = [
            'title' => 'تفاصيل الوصفة',
            'prescription' => $prescription,
            'medications' => $this->prescriptionModel->getMedications($prescriptionId)
        ];

        $this->view('doctor/prescription_details', $data);
    }

    /**
     * البحث في المرضى
     */
    public function searchPatients()
    {
        $query = App::get('q', '');
        
        if (empty($query)) {
            $this->json(['patients' => []]);
            return;
        }

        $patients = $this->userModel->search($query, 'patient');
        
        $this->json(['patients' => $patients]);
    }

    /**
     * إضافة فحص مخبري
     */
    public function addLabTest()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $doctorId = $this->currentUser['id'];
        $postData = App::post();
        
        $testData = [
            'patient_id' => $postData['patient_id'],
            'doctor_id' => $doctorId,
            'test_name' => $postData['test_name'],
            'test_date' => $postData['test_date'] ?? date('Y-m-d'),
            'notes' => $postData['notes'] ?? null,
            'status' => 'ordered'
        ];

        $testId = $this->medicalRecordModel->addLabTest($testData);

        if ($testId) {
            $this->json(['success' => true, 'message' => 'تم طلب الفحص المخبري بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في طلب الفحص المخبري'], 500);
        }
    }

    /**
     * تحديث نتيجة الفحص المخبري
     */
    public function updateLabTestResult()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $testId = App::post('test_id');
        $results = App::post('results');
        
        if (!$testId || !$results) {
            $this->json(['success' => false, 'message' => 'البيانات المطلوبة ناقصة'], 400);
            return;
        }

        if ($this->medicalRecordModel->updateLabTestResult($testId, $results)) {
            $this->json(['success' => true, 'message' => 'تم تحديث نتيجة الفحص بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تحديث نتيجة الفحص'], 500);
        }
    }

    /**
     * الإشعارات
     */
    public function notifications()
    {
        $doctorId = $this->currentUser['id'];
        
        $data = [
            'title' => 'الإشعارات',
            'notifications' => $this->notificationModel->getByUser($doctorId, false, 50),
            'unreadCount' => $this->notificationModel->getUnreadCount($doctorId)
        ];

        $this->view('doctor/notifications', $data);
    }

    /**
     * التقارير
     */
    public function reports()
    {
        $doctorId = $this->currentUser['id'];
        $startDate = App::get('start_date', date('Y-m-01'));
        $endDate = App::get('end_date', date('Y-m-t'));
        
        $data = [
            'title' => 'التقارير',
            'startDate' => $startDate,
            'endDate' => $endDate,
            'appointmentReport' => $this->appointmentModel->getReport($startDate, $endDate, $doctorId),
            'prescriptionReport' => $this->prescriptionModel->getReport($startDate, $endDate, $doctorId),
            'stats' => [
                'appointments' => $this->appointmentModel->getStats($doctorId),
                'prescriptions' => $this->prescriptionModel->getStats($doctorId),
                'medical_records' => $this->medicalRecordModel->getStats($doctorId)
            ]
        ];

        $this->view('doctor/reports', $data);
    }

    /**
     * الملف الشخصي
     */
    public function profile()
    {
        if (App::isPost()) {
            $this->updateProfile();
            return;
        }

        $data = [
            'title' => 'الملف الشخصي',
            'doctor' => $this->userModel->findById($this->currentUser['id'])
        ];

        $this->view('doctor/profile', $data);
    }

    /**
     * تحديث الملف الشخصي
     */
    private function updateProfile()
    {
        $doctorId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->userModel->validate($postData, true);

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('doctor/profile');
            return;
        }

        // تحديث البيانات
        $updateData = [
            'first_name' => $postData['first_name'],
            'last_name' => $postData['last_name'],
            'phone' => $postData['phone'],
            'email' => $postData['email'],
            'address' => $postData['address'] ?? null
        ];

        if ($this->userModel->update($doctorId, $updateData)) {
            // تحديث بيانات الجلسة
            $_SESSION['user_name'] = $postData['first_name'] . ' ' . $postData['last_name'];
            
            $this->setFlashMessage('تم تحديث الملف الشخصي بنجاح', 'success');
        } else {
            $this->setFlashMessage('حدث خطأ أثناء تحديث الملف الشخصي', 'error');
        }

        $this->redirect('doctor/profile');
    }
}
