<?php

/**
 * نموذج المستخدم
 * يتعامل مع جدول المستخدمين وجميع العمليات المتعلقة بهم
 */
class User
{
    private $db;
    private $table = 'users';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على مستخدم بواسطة ID
     */
    public function findById($id)
    {
        $query = "SELECT u.*, d.specialization, d.license_number as doctor_license, d.years_of_experience,
                         p.pharmacy_name, p.license_number as pharmacist_license, p.pharmacy_address
                  FROM {$this->table} u
                  LEFT JOIN doctors d ON u.id = d.user_id
                  LEFT JOIN pharmacists p ON u.id = p.user_id
                  WHERE u.id = :id";
        
        return $this->db->selectOne($query, [':id' => $id]);
    }

    /**
     * الحصول على مستخدم بواسطة البريد الإلكتروني
     */
    public function findByEmail($email)
    {
        $query = "SELECT * FROM {$this->table} WHERE email = :email";
        return $this->db->selectOne($query, [':email' => $email]);
    }

    /**
     * الحصول على مستخدم بواسطة الرقم الوطني
     */
    public function findByNationalId($nationalId)
    {
        $query = "SELECT * FROM {$this->table} WHERE national_id = :national_id";
        return $this->db->selectOne($query, [':national_id' => $nationalId]);
    }

    /**
     * إنشاء مستخدم جديد
     */
    public function create($data)
    {
        $query = "INSERT INTO {$this->table} (email, password, user_type, first_name, last_name, 
                                            phone, date_of_birth, gender, national_id, address, created_at) 
                  VALUES (:email, :password, :user_type, :first_name, :last_name, 
                          :phone, :date_of_birth, :gender, :national_id, :address, NOW())";

        $params = [
            ':email' => $data['email'],
            ':password' => password_hash($data['password'], PASSWORD_DEFAULT),
            ':user_type' => $data['user_type'],
            ':first_name' => $data['first_name'],
            ':last_name' => $data['last_name'],
            ':phone' => $data['phone'] ?? null,
            ':date_of_birth' => $data['date_of_birth'] ?? null,
            ':gender' => $data['gender'] ?? null,
            ':national_id' => $data['national_id'] ?? null,
            ':address' => $data['address'] ?? null
        ];

        return $this->db->insert($query, $params);
    }

    /**
     * تحديث بيانات المستخدم
     */
    public function update($id, $data)
    {
        $fields = [];
        $params = [':id' => $id];

        foreach ($data as $key => $value) {
            if ($key !== 'id' && $key !== 'password') {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if (empty($fields)) {
            return false;
        }

        $query = "UPDATE {$this->table} SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = :id";
        return $this->db->update($query, $params) > 0;
    }

    /**
     * تحديث كلمة المرور
     */
    public function updatePassword($id, $newPassword)
    {
        $query = "UPDATE {$this->table} SET password = :password, updated_at = NOW() WHERE id = :id";
        $params = [
            ':id' => $id,
            ':password' => password_hash($newPassword, PASSWORD_DEFAULT)
        ];

        return $this->db->update($query, $params) > 0;
    }

    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggleActive($id)
    {
        $query = "UPDATE {$this->table} SET is_active = NOT is_active, updated_at = NOW() WHERE id = :id";
        return $this->db->update($query, [':id' => $id]) > 0;
    }

    /**
     * حذف المستخدم
     */
    public function delete($id)
    {
        $query = "DELETE FROM {$this->table} WHERE id = :id";
        return $this->db->delete($query, [':id' => $id]) > 0;
    }

    /**
     * الحصول على جميع المستخدمين مع التصفية
     */
    public function getAll($filters = [])
    {
        $query = "SELECT u.*, d.specialization, p.pharmacy_name 
                  FROM {$this->table} u
                  LEFT JOIN doctors d ON u.id = d.user_id
                  LEFT JOIN pharmacists p ON u.id = p.user_id
                  WHERE 1=1";
        
        $params = [];

        // تطبيق المرشحات
        if (!empty($filters['user_type'])) {
            $query .= " AND u.user_type = :user_type";
            $params[':user_type'] = $filters['user_type'];
        }

        if (!empty($filters['is_active'])) {
            $query .= " AND u.is_active = :is_active";
            $params[':is_active'] = $filters['is_active'];
        }

        if (!empty($filters['search'])) {
            $query .= " AND (u.first_name LIKE :search OR u.last_name LIKE :search OR u.email LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        $query .= " ORDER BY u.created_at DESC";

        // تطبيق التصفح
        if (!empty($filters['limit'])) {
            $query .= " LIMIT " . (int)$filters['limit'];
            
            if (!empty($filters['offset'])) {
                $query .= " OFFSET " . (int)$filters['offset'];
            }
        }

        return $this->db->select($query, $params);
    }

    /**
     * عدد المستخدمين مع التصفية
     */
    public function count($filters = [])
    {
        $query = "SELECT COUNT(*) as count FROM {$this->table} WHERE 1=1";
        $params = [];

        if (!empty($filters['user_type'])) {
            $query .= " AND user_type = :user_type";
            $params[':user_type'] = $filters['user_type'];
        }

        if (!empty($filters['is_active'])) {
            $query .= " AND is_active = :is_active";
            $params[':is_active'] = $filters['is_active'];
        }

        if (!empty($filters['search'])) {
            $query .= " AND (first_name LIKE :search OR last_name LIKE :search OR email LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        $result = $this->db->selectOne($query, $params);
        return (int)$result['count'];
    }

    /**
     * الحصول على المرضى للطبيب
     */
    public function getPatientsByDoctor($doctorId)
    {
        $query = "SELECT DISTINCT u.* 
                  FROM {$this->table} u
                  INNER JOIN appointments a ON u.id = a.patient_id
                  WHERE a.doctor_id = :doctor_id AND u.user_type = 'patient' AND u.is_active = 1
                  ORDER BY u.first_name, u.last_name";
        
        return $this->db->select($query, [':doctor_id' => $doctorId]);
    }

    /**
     * الحصول على الأطباء النشطين
     */
    public function getActiveDoctors()
    {
        $query = "SELECT u.*, d.specialization, d.years_of_experience 
                  FROM {$this->table} u
                  INNER JOIN doctors d ON u.id = d.user_id
                  WHERE u.user_type = 'doctor' AND u.is_active = 1
                  ORDER BY u.first_name, u.last_name";
        
        return $this->db->select($query);
    }

    /**
     * الحصول على الصيادلة النشطين
     */
    public function getActivePharmacists()
    {
        $query = "SELECT u.*, p.pharmacy_name, p.pharmacy_address 
                  FROM {$this->table} u
                  INNER JOIN pharmacists p ON u.id = p.user_id
                  WHERE u.user_type = 'pharmacist' AND u.is_active = 1
                  ORDER BY u.first_name, u.last_name";
        
        return $this->db->select($query);
    }

    /**
     * البحث في المستخدمين
     */
    public function search($term, $userType = null)
    {
        $query = "SELECT u.*, d.specialization, p.pharmacy_name 
                  FROM {$this->table} u
                  LEFT JOIN doctors d ON u.id = d.user_id
                  LEFT JOIN pharmacists p ON u.id = p.user_id
                  WHERE (u.first_name LIKE :term OR u.last_name LIKE :term OR u.email LIKE :term OR u.national_id LIKE :term)
                  AND u.is_active = 1";
        
        $params = [':term' => "%$term%"];

        if ($userType) {
            $query .= " AND u.user_type = :user_type";
            $params[':user_type'] = $userType;
        }

        $query .= " ORDER BY u.first_name, u.last_name LIMIT 20";

        return $this->db->select($query, $params);
    }

    /**
     * إحصائيات المستخدمين
     */
    public function getStats()
    {
        $stats = [];

        // إجمالي المستخدمين
        $total = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->table} WHERE is_active = 1");
        $stats['total'] = (int)$total['count'];

        // المستخدمين حسب النوع
        $byType = $this->db->select(
            "SELECT user_type, COUNT(*) as count FROM {$this->table} WHERE is_active = 1 GROUP BY user_type"
        );

        foreach ($byType as $type) {
            $stats[$type['user_type']] = (int)$type['count'];
        }

        // المستخدمين الجدد هذا الشهر
        $newThisMonth = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE is_active = 1 AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())"
        );
        $stats['new_this_month'] = (int)$newThisMonth['count'];

        return $stats;
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validate($data, $isUpdate = false)
    {
        $errors = [];

        // التحقق من الحقول المطلوبة
        if (!$isUpdate) {
            $required = ['email', 'password', 'user_type', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    $errors[$field] = "حقل $field مطلوب";
                }
            }
        }

        // التحقق من البريد الإلكتروني
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'البريد الإلكتروني غير صحيح';
        }

        // التحقق من كلمة المرور
        if (!empty($data['password']) && strlen($data['password']) < 8) {
            $errors['password'] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }

        // التحقق من نوع المستخدم
        if (!empty($data['user_type']) && !in_array($data['user_type'], ['patient', 'doctor', 'pharmacist', 'admin'])) {
            $errors['user_type'] = 'نوع المستخدم غير صحيح';
        }

        // التحقق من الأسماء
        if (!empty($data['first_name']) && (strlen($data['first_name']) < 2 || strlen($data['first_name']) > 50)) {
            $errors['first_name'] = 'الاسم الأول يجب أن يكون بين 2-50 حرف';
        }

        if (!empty($data['last_name']) && (strlen($data['last_name']) < 2 || strlen($data['last_name']) > 50)) {
            $errors['last_name'] = 'الاسم الأخير يجب أن يكون بين 2-50 حرف';
        }

        return $errors;
    }

    /**
     * الحصول على اسم المستخدم الكامل
     */
    public static function getFullName($user)
    {
        if (is_array($user)) {
            return trim($user['first_name'] . ' ' . $user['last_name']);
        }
        return '';
    }

    /**
     * الحصول على نوع المستخدم باللغة العربية
     */
    public static function getUserTypeLabel($userType)
    {
        $types = [
            'patient' => 'مريض',
            'doctor' => 'طبيب',
            'pharmacist' => 'صيدلي',
            'admin' => 'مدير'
        ];

        return $types[$userType] ?? $userType;
    }

    /**
     * التحقق من كلمة المرور
     */
    public function verifyPassword($userId, $password)
    {
        $user = $this->findById($userId);
        if ($user) {
            return password_verify($password, $user['password']);
        }
        return false;
    }
}
