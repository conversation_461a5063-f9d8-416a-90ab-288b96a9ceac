<div class="row justify-content-center">
    <div class="col-md-6">
        <h2 class="text-center mb-4">
            <i class="bi bi-box-arrow-in-right me-2"></i>
            تسجيل الدخول
        </h2>
        
        <?php if (isset($errors['login'])): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($errors['login']) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="<?= App::url('login') ?>" data-validate="true">
            <div class="mb-3">
                <label for="email" class="form-label">
                    <i class="bi bi-envelope me-2"></i>
                    البريد الإلكتروني
                </label>
                <input 
                    type="email" 
                    class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                    id="email" 
                    name="email" 
                    value="<?= htmlspecialchars($old_input['email'] ?? '') ?>"
                    required
                    placeholder="أدخل بريدك الإلكتروني"
                >
                <?php if (isset($errors['email'])): ?>
                    <div class="invalid-feedback">
                        <?= htmlspecialchars($errors['email']) ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">
                    <i class="bi bi-lock me-2"></i>
                    كلمة المرور
                </label>
                <div class="password-field">
                    <input 
                        type="password" 
                        class="form-control <?= isset($errors['password']) ? 'is-invalid' : '' ?>" 
                        id="password" 
                        name="password" 
                        required
                        placeholder="أدخل كلمة المرور"
                    >
                    <button type="button" class="password-toggle">
                        <i class="bi bi-eye"></i>
                    </button>
                </div>
                <?php if (isset($errors['password'])): ?>
                    <div class="invalid-feedback">
                        <?= htmlspecialchars($errors['password']) ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                <label class="form-check-label" for="remember">
                    تذكرني لمدة 30 يوم
                </label>
            </div>
            
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    تسجيل الدخول
                </button>
            </div>
        </form>
        
        <div class="auth-links">
            <p class="mb-2">ليس لديك حساب؟</p>
            <a href="<?= App::url('register') ?>" class="btn btn-outline-primary">
                <i class="bi bi-person-plus me-2"></i>
                إنشاء حساب جديد
            </a>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?= App::url() ?>" class="text-muted">
                <i class="bi bi-arrow-right me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>
    </div>
</div>

<!-- معلومات الحسابات التجريبية للاختبار -->
<?php if (defined('APP_DEBUG') && APP_DEBUG): ?>
<div class="row justify-content-center mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    حسابات تجريبية للاختبار
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h6 class="text-primary">المدير</h6>
                        <small class="text-muted">
                            <EMAIL><br>
                            password
                        </small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success">الطبيب</h6>
                        <small class="text-muted">
                            <EMAIL><br>
                            password
                        </small>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <h6 class="text-info">الصيدلي</h6>
                        <small class="text-muted">
                            <EMAIL><br>
                            password
                        </small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-warning">المريض</h6>
                        <small class="text-muted">
                            <EMAIL><br>
                            password
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
