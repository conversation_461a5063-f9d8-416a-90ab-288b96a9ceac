<?php

/**
 * متحكم الصيدلي
 * يعالج جميع العمليات المتعلقة بالصيادلة
 */
class PharmacistController extends Controller
{
    private $userModel;
    private $prescriptionModel;
    private $notificationModel;

    public function __construct()
    {
        parent::__construct();
        
        // التحقق من تسجيل الدخول ونوع المستخدم
        $this->requireAuth();
        $this->requireUserType('pharmacist');
        
        // تحميل النماذج
        $this->userModel = new User();
        $this->prescriptionModel = new Prescription();
        $this->notificationModel = new Notification();
    }

    /**
     * لوحة تحكم الصيدلي
     */
    public function dashboard()
    {
        $pharmacistId = $this->currentUser['id'];
        
        // الحصول على البيانات للوحة التحكم
        $data = [
            'title' => 'لوحة تحكم الصيدلي',
            'pharmacist' => $this->currentUser,
            'activePrescriptions' => $this->prescriptionModel->getActive(10),
            'recentDispensed' => $this->getRecentDispensedMedications(10),
            'notifications' => $this->notificationModel->getByUser($pharmacistId, false, 5),
            'unreadNotifications' => $this->notificationModel->getUnreadCount($pharmacistId),
            'stats' => $this->getPrescriptionStats()
        ];

        $this->view('pharmacist/dashboard', $data);
    }

    /**
     * البحث في الوصفات
     */
    public function prescriptions()
    {
        $search = App::get('search', '');
        $status = App::get('status', '');
        
        $prescriptions = [];
        
        if (!empty($search)) {
            $prescriptions = $this->prescriptionModel->search($search, ['status' => $status]);
        } elseif (!empty($status)) {
            if ($status === 'active') {
                $prescriptions = $this->prescriptionModel->getActive();
            } elseif ($status === 'expired') {
                $prescriptions = $this->prescriptionModel->getExpired();
            }
        }

        $data = [
            'title' => 'البحث في الوصفات',
            'prescriptions' => $prescriptions,
            'search' => $search,
            'currentStatus' => $status
        ];

        $this->view('pharmacist/prescriptions', $data);
    }

    /**
     * عرض تفاصيل الوصفة
     */
    public function viewPrescription($prescriptionId)
    {
        $prescription = $this->prescriptionModel->findById($prescriptionId);
        
        if (!$prescription) {
            $this->setFlashMessage('الوصفة غير موجودة', 'error');
            $this->redirect('pharmacist/prescriptions');
            return;
        }

        $data = [
            'title' => 'تفاصيل الوصفة - ' . $prescription['prescription_code'],
            'prescription' => $prescription,
            'medications' => $this->prescriptionModel->getMedications($prescriptionId)
        ];

        $this->view('pharmacist/prescription_details', $data);
    }

    /**
     * البحث في الوصفة بالكود
     */
    public function searchPrescription()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $prescriptionCode = App::post('prescription_code');
        
        if (empty($prescriptionCode)) {
            $this->json(['success' => false, 'message' => 'كود الوصفة مطلوب'], 400);
            return;
        }

        $prescription = $this->prescriptionModel->findByCode($prescriptionCode);
        
        if (!$prescription) {
            $this->json(['success' => false, 'message' => 'الوصفة غير موجودة'], 404);
            return;
        }

        // التحقق من صلاحية الوصفة
        if ($prescription['status'] !== 'active') {
            $this->json(['success' => false, 'message' => 'الوصفة غير نشطة'], 400);
            return;
        }

        if (strtotime($prescription['expiry_date']) < time()) {
            $this->json(['success' => false, 'message' => 'الوصفة منتهية الصلاحية'], 400);
            return;
        }

        $medications = $this->prescriptionModel->getMedications($prescription['id']);

        $this->json([
            'success' => true,
            'prescription' => $prescription,
            'medications' => $medications
        ]);
    }

    /**
     * صرف دواء
     */
    public function dispenseMedication()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $medicationId = App::post('medication_id');
        $dispensedQuantity = App::post('dispensed_quantity');
        $pharmacistId = $this->currentUser['id'];

        if (!$medicationId) {
            $this->json(['success' => false, 'message' => 'معرف الدواء مطلوب'], 400);
            return;
        }

        // الحصول على معلومات الدواء
        $medication = $this->db->selectOne(
            "SELECT pm.*, p.patient_id, p.prescription_code 
             FROM prescription_medications pm 
             JOIN prescriptions p ON pm.prescription_id = p.id 
             WHERE pm.id = :id",
            [':id' => $medicationId]
        );

        if (!$medication) {
            $this->json(['success' => false, 'message' => 'الدواء غير موجود'], 404);
            return;
        }

        if ($medication['is_dispensed']) {
            $this->json(['success' => false, 'message' => 'تم صرف هذا الدواء بالفعل'], 400);
            return;
        }

        // صرف الدواء
        if ($this->prescriptionModel->dispenseMedication($medicationId, $pharmacistId, $dispensedQuantity)) {
            // إرسال إشعار للمريض
            $this->notificationModel->notifyMedicationDispensed(
                $medication['patient_id'],
                $medication['medication_name'],
                $this->currentUser['pharmacy_name'] ?? 'الصيدلية'
            );

            $this->json(['success' => true, 'message' => 'تم صرف الدواء بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في صرف الدواء'], 500);
        }
    }

    /**
     * التحقق من التفاعلات الدوائية
     */
    public function checkDrugInteractions()
    {
        if (!App::isPost()) {
            $this->json(['interactions' => []], 405);
            return;
        }

        $medications = App::post('medications', []);
        
        if (empty($medications)) {
            $this->json(['interactions' => []]);
            return;
        }

        $interactions = $this->prescriptionModel->checkDrugInteractions($medications);
        
        $this->json(['interactions' => $interactions]);
    }

    /**
     * تقرير الصرف
     */
    public function dispensingReport()
    {
        $startDate = App::get('start_date', date('Y-m-01'));
        $endDate = App::get('end_date', date('Y-m-t'));
        $pharmacistId = $this->currentUser['id'];
        
        $dispensedMedications = $this->getDispensedMedications($startDate, $endDate, $pharmacistId);
        
        $data = [
            'title' => 'تقرير الصرف',
            'startDate' => $startDate,
            'endDate' => $endDate,
            'dispensedMedications' => $dispensedMedications,
            'stats' => $this->getDispensedStats($startDate, $endDate, $pharmacistId)
        ];

        $this->view('pharmacist/dispensing_report', $data);
    }

    /**
     * الإشعارات
     */
    public function notifications()
    {
        $pharmacistId = $this->currentUser['id'];
        
        $data = [
            'title' => 'الإشعارات',
            'notifications' => $this->notificationModel->getByUser($pharmacistId, false, 50),
            'unreadCount' => $this->notificationModel->getUnreadCount($pharmacistId)
        ];

        $this->view('pharmacist/notifications', $data);
    }

    /**
     * الملف الشخصي
     */
    public function profile()
    {
        if (App::isPost()) {
            $this->updateProfile();
            return;
        }

        $data = [
            'title' => 'الملف الشخصي',
            'pharmacist' => $this->userModel->findById($this->currentUser['id'])
        ];

        $this->view('pharmacist/profile', $data);
    }

    /**
     * تحديث الملف الشخصي
     */
    private function updateProfile()
    {
        $pharmacistId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->userModel->validate($postData, true);

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('pharmacist/profile');
            return;
        }

        // تحديث البيانات الأساسية
        $updateData = [
            'first_name' => $postData['first_name'],
            'last_name' => $postData['last_name'],
            'phone' => $postData['phone'],
            'email' => $postData['email'],
            'address' => $postData['address'] ?? null
        ];

        $success = $this->userModel->update($pharmacistId, $updateData);

        // تحديث بيانات الصيدلي
        if ($success && !empty($postData['pharmacy_name'])) {
            $this->db->update(
                "UPDATE pharmacists SET pharmacy_name = :pharmacy_name, pharmacy_address = :pharmacy_address 
                 WHERE user_id = :user_id",
                [
                    ':pharmacy_name' => $postData['pharmacy_name'],
                    ':pharmacy_address' => $postData['pharmacy_address'] ?? null,
                    ':user_id' => $pharmacistId
                ]
            );
        }

        if ($success) {
            // تحديث بيانات الجلسة
            $_SESSION['user_name'] = $postData['first_name'] . ' ' . $postData['last_name'];
            
            $this->setFlashMessage('تم تحديث الملف الشخصي بنجاح', 'success');
        } else {
            $this->setFlashMessage('حدث خطأ أثناء تحديث الملف الشخصي', 'error');
        }

        $this->redirect('pharmacist/profile');
    }

    /**
     * الحصول على الأدوية المصروفة حديثاً
     */
    private function getRecentDispensedMedications($limit = 10)
    {
        $pharmacistId = $this->currentUser['id'];
        
        return $this->db->select(
            "SELECT pm.*, p.prescription_code, p.patient_id,
                    CONCAT(u.first_name, ' ', u.last_name) as patient_name
             FROM prescription_medications pm
             JOIN prescriptions p ON pm.prescription_id = p.id
             JOIN users u ON p.patient_id = u.id
             WHERE pm.is_dispensed = 1 AND pm.dispensed_by = :pharmacist_id
             ORDER BY pm.dispensed_at DESC
             LIMIT :limit",
            [':pharmacist_id' => $pharmacistId, ':limit' => $limit]
        );
    }

    /**
     * الحصول على إحصائيات الوصفات
     */
    private function getPrescriptionStats()
    {
        $stats = [];
        
        // إجمالي الوصفات النشطة
        $activePrescriptions = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM prescriptions 
             WHERE status = 'active' AND expiry_date >= CURDATE()"
        );
        $stats['active_prescriptions'] = (int)$activePrescriptions['count'];

        // الوصفات المنتهية الصلاحية
        $expiredPrescriptions = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM prescriptions 
             WHERE status = 'active' AND expiry_date < CURDATE()"
        );
        $stats['expired_prescriptions'] = (int)$expiredPrescriptions['count'];

        // الأدوية المصروفة اليوم
        $pharmacistId = $this->currentUser['id'];
        $todayDispensed = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM prescription_medications 
             WHERE is_dispensed = 1 AND dispensed_by = :pharmacist_id 
             AND DATE(dispensed_at) = CURDATE()",
            [':pharmacist_id' => $pharmacistId]
        );
        $stats['today_dispensed'] = (int)$todayDispensed['count'];

        // إجمالي الأدوية المصروفة
        $totalDispensed = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM prescription_medications 
             WHERE is_dispensed = 1 AND dispensed_by = :pharmacist_id",
            [':pharmacist_id' => $pharmacistId]
        );
        $stats['total_dispensed'] = (int)$totalDispensed['count'];

        return $stats;
    }

    /**
     * الحصول على الأدوية المصروفة في فترة معينة
     */
    private function getDispensedMedications($startDate, $endDate, $pharmacistId)
    {
        return $this->db->select(
            "SELECT pm.*, p.prescription_code, p.patient_id,
                    CONCAT(u.first_name, ' ', u.last_name) as patient_name,
                    CONCAT(d.first_name, ' ', d.last_name) as doctor_name
             FROM prescription_medications pm
             JOIN prescriptions p ON pm.prescription_id = p.id
             JOIN users u ON p.patient_id = u.id
             JOIN users d ON p.doctor_id = d.id
             WHERE pm.is_dispensed = 1 AND pm.dispensed_by = :pharmacist_id
             AND DATE(pm.dispensed_at) BETWEEN :start_date AND :end_date
             ORDER BY pm.dispensed_at DESC",
            [
                ':pharmacist_id' => $pharmacistId,
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ]
        );
    }

    /**
     * الحصول على إحصائيات الصرف في فترة معينة
     */
    private function getDispensedStats($startDate, $endDate, $pharmacistId)
    {
        $stats = [];
        
        // إجمالي الأدوية المصروفة
        $totalDispensed = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM prescription_medications 
             WHERE is_dispensed = 1 AND dispensed_by = :pharmacist_id
             AND DATE(dispensed_at) BETWEEN :start_date AND :end_date",
            [
                ':pharmacist_id' => $pharmacistId,
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ]
        );
        $stats['total_dispensed'] = (int)$totalDispensed['count'];

        // الأدوية الأكثر صرفاً
        $topMedications = $this->db->select(
            "SELECT medication_name, COUNT(*) as count 
             FROM prescription_medications 
             WHERE is_dispensed = 1 AND dispensed_by = :pharmacist_id
             AND DATE(dispensed_at) BETWEEN :start_date AND :end_date
             GROUP BY medication_name 
             ORDER BY count DESC 
             LIMIT 5",
            [
                ':pharmacist_id' => $pharmacistId,
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ]
        );
        $stats['top_medications'] = $topMedications;

        // الصرف اليومي
        $dailyDispensing = $this->db->select(
            "SELECT DATE(dispensed_at) as date, COUNT(*) as count 
             FROM prescription_medications 
             WHERE is_dispensed = 1 AND dispensed_by = :pharmacist_id
             AND DATE(dispensed_at) BETWEEN :start_date AND :end_date
             GROUP BY DATE(dispensed_at) 
             ORDER BY date",
            [
                ':pharmacist_id' => $pharmacistId,
                ':start_date' => $startDate,
                ':end_date' => $endDate
            ]
        );
        $stats['daily_dispensing'] = $dailyDispensing;

        return $stats;
    }

    /**
     * تسجيل الإشعار كمقروء
     */
    public function markNotificationRead()
    {
        $notificationId = App::post('notification_id');
        
        if (!$notificationId) {
            $this->json(['success' => false, 'message' => 'معرف الإشعار مطلوب'], 400);
            return;
        }

        // التحقق من ملكية الإشعار
        $notification = $this->notificationModel->findById($notificationId);
        if (!$notification || $notification['user_id'] != $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'الإشعار غير موجود'], 404);
            return;
        }

        if ($this->notificationModel->markAsRead($notificationId)) {
            $this->json(['success' => true, 'message' => 'تم تسجيل الإشعار كمقروء']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تحديث الإشعار'], 500);
        }
    }

    /**
     * تسجيل جميع الإشعارات كمقروءة
     */
    public function markAllNotificationsRead()
    {
        $pharmacistId = $this->currentUser['id'];
        
        $count = $this->notificationModel->markAllAsRead($pharmacistId);
        
        $this->json([
            'success' => true, 
            'message' => "تم تسجيل $count إشعار كمقروء",
            'count' => $count
        ]);
    }

    /**
     * تصدير تقرير الصرف
     */
    public function exportDispensingReport()
    {
        $startDate = App::get('start_date', date('Y-m-01'));
        $endDate = App::get('end_date', date('Y-m-t'));
        $format = App::get('format', 'csv');
        $pharmacistId = $this->currentUser['id'];
        
        $dispensedMedications = $this->getDispensedMedications($startDate, $endDate, $pharmacistId);
        
        if ($format === 'csv') {
            $this->exportToCSV($dispensedMedications, $startDate, $endDate);
        } else {
            $this->json(['success' => false, 'message' => 'تنسيق غير مدعوم'], 400);
        }
    }

    /**
     * تصدير البيانات إلى CSV
     */
    private function exportToCSV($data, $startDate, $endDate)
    {
        $filename = "dispensing_report_{$startDate}_to_{$endDate}.csv";
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // إضافة BOM للدعم العربي
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // العناوين
        fputcsv($output, [
            'كود الوصفة',
            'اسم المريض',
            'اسم الطبيب',
            'اسم الدواء',
            'الجرعة',
            'الكمية المصروفة',
            'تاريخ الصرف'
        ]);
        
        // البيانات
        foreach ($data as $row) {
            fputcsv($output, [
                $row['prescription_code'],
                $row['patient_name'],
                $row['doctor_name'],
                $row['medication_name'],
                $row['dosage'],
                $row['dispensed_quantity'],
                DateHelper::formatArabic($row['dispensed_at'])
            ]);
        }
        
        fclose($output);
        exit;
    }
}
